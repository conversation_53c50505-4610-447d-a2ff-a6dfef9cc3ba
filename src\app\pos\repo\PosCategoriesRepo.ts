import {
    APP_LOCAL_DB_COLLECTIONS,
    AppLocalDB,
} from "../../../common/config/localDB";
import { EndPointsConstants } from "../../../common/constants/ApiConstants";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { ICategoryModel } from "../../../common/models/CategoryModel";
import { debug } from "../../../common/utils/CommonUtils";

export class PosCategoriesRepo {
    static async getAndCacheCategories(updatedAt?: Date) {
        try {
            const res = await AxiosHelper.get<ICategoryModel[]>(
                EndPointsConstants.CATEGORIES,
                { params: { updatedAt } }
            );

            if (!res.success) throw new Error(res.message);
            const data = res.data || [];

            if (!updatedAt) await AppLocalDB.set(APP_LOCAL_DB_COLLECTIONS.CATEGORIES, data);
            else await AppLocalDB.add(APP_LOCAL_DB_COLLECTIONS.CATEGORIES, data, true);
        } catch (error) {
            debug(`PosCategoriesRepo [getAndCacheCategories] Error: `, error);
            throw error;
        }
    }

    static async getCategories(activeOnly: boolean = false) {
        try {
            const res = await AppLocalDB.get<ICategoryModel>(
                APP_LOCAL_DB_COLLECTIONS.CATEGORIES,
                { where: activeOnly ? [[["active", "==", true]]] : undefined }
            );

            if (!res) {
                throw new Error("No categories found");
            }

            return { data: res };
        } catch (error) {
            debug(`PosCategoriesRepo [getCategories] Error: `, error);
            throw error;
        }
    }
}
