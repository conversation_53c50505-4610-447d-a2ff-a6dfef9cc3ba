import { IInvoicePrinterItemModel } from "./InvoicePrinterItemModel";

export interface IInvoicePrinterBodyModel {
    organizationName: string;
    organizationSubName: string;
    orderType: string;
    invoiceNumber: string;
    orderNumber: string;
    items: IInvoicePrinterItemModel[];
    address?: string;
    vatNo?: string;
    crNo?: string;
    phone?: string;
    invoiceTitle: string;
    subTotal: string;
    discount: string;
    tobaccoTax?: string;
    vat: string;
    total: string;
    cash: string;
    network: string;
    totalDeliverApp: string;
    qrCode: string;
    footer?: string;
    customerName?: string;
    customerMobile?: string;
    customerAddress?: string;
    customerTaxNumber?: string;
}