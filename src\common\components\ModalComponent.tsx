import { modalSelector } from "../redux/data/selector";
import { useAppSelector } from "../redux/store";
import useActions from "../redux/data/useActions";
import { isMobile } from "react-device-detect";
import ListComponent from "./ListComponent";
import { FC } from "react";
import { TranslateConstants } from "../constants/TranslateConstants";
import ButtonComponent from "./ButtonComponent";
import { useTranslate } from "../hooks/useTranslate";

export interface IModalProps {
    show: boolean;
    closeOnBlur?: boolean;
    component: JSX.Element | null;
    title?: string;
    size?:
    | "sm"
    | "md"
    | "lg"
    | "xl"
    | "2xl"
    | "3xl"
    | "4xl"
    | "5xl"
    | "6xl"
    | "7xl";
    submitButton?: {
        text: string;
        onClick: () => void;
    };
    closButtonClassName?: string;
    showButtons?: boolean;
    onClose?: () => void;
}

interface IModalButtonProps {
    text?: string;
    component?: React.ReactNode;
    isDisabled?: boolean;
    onClick?: () => void;
    onClose?: () => void;
    closButtonClassName?: string;
}

export const ModalButtonsComponent: FC<IModalButtonProps> = ({
    text,
    component,
    isDisabled,
    onClick,
    onClose,
    closButtonClassName = '',
}) => {
    const { closeModal } = useActions();

    return (
        <div className="flex justify-between gap-2 font-semibold text-white border-t pt-2 m-2">
            {(!!text || !!component) && (
                <ButtonComponent
                    text={text}
                    component={component}
                    onClick={onClick}
                    className="font-normal"
                    type="submit"
                    isDisabled={isDisabled}
                />
            )}
            <ButtonComponent
                text={TranslateConstants.CLOSE}
                className={"font-normal" + " " + closButtonClassName}
                bgColor="slate"
                onClick={() => {
                    if (onClose) {
                        onClose();
                    }
                    closeModal();
                }}
            />
        </div>
    );
};

const ModalComponent = () => {
    const { translate } = useTranslate();
    const {
        show,
        component,
        title,
        size,
        submitButton,
        showButtons,
        onClose,
        closeOnBlur,
        closButtonClassName,
    } = useAppSelector(modalSelector);
    const { closeModal } = useActions();

    const handelMaxWidth = (): string => {
        if (isMobile) {
            return "95%";
        }

        const sizes = [
            { name: "sm", value: "24rem" },
            { name: "md", value: "28rem" },
            { name: "lg", value: "32rem" },
            { name: "xl", value: "36rem" },
            { name: "2xl", value: "42rem" },
            { name: "3xl", value: "48rem" },
            { name: "4xl", value: "56rem" },
            { name: "5xl", value: "64rem" },
            { name: "6xl", value: "72rem" },
            { name: "7xl", value: "80rem" },
        ];
        const s = sizes.find((el) => el.name === size);
        return s?.value ?? sizes[4].value;
    };

    if (!show || !component) {
        return null;
    }

    return (
        <div className="h-s w-screen bg-black dark:bg-gray-600 fixed top-0 bg-opacity-70 dark:bg-opacity-70 z-10">
            <div
                className="flex justify-center items-center h-full w-full relative"
                // close the modal when clicking outside the modal
                onClick={() => {
                    if (!closeOnBlur) return;
                    if (onClose) onClose();
                    closeModal();
                }}
            >
                <div
                    onClick={(e) => {
                        e.stopPropagation();
                    }}
                    className="bg-white dark:bg-base-100 dark:border dark:border-gray-500 max-h-[85%] rounded-md flex flex-col"
                    style={{ width: handelMaxWidth() }}
                >
                    {!!title && (
                        <div className="text-center font-semibold py-2 border-b text-lg mx-2">
                            {translate(title)}
                        </div>
                    )}
                    <ListComponent padding={!title ? "0" : "0 pt-2"} space="0">
                        {component}
                    </ListComponent>
                    {showButtons && (
                        <ModalButtonsComponent
                            onClick={submitButton?.onClick}
                            text={submitButton?.text}
                            onClose={onClose}
                            closButtonClassName={closButtonClassName}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default ModalComponent;
