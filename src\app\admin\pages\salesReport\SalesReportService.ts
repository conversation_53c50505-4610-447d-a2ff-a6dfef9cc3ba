import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import { IOrderModel } from "../../../../common/models/OrderModel";
import { IOrderSumModel } from "../../../../common/models/OrderSumModel";
import { PdfMakeUtils } from "../../../../common/printers/PdfMakeUtils";
import { SalesReportPdfPrinterContent } from "../../../../common/printers/slices/salesReport/SalesReportPdfPrinterContent";
import { ISalesReportPdfPrinterModel } from "../../../../common/printers/slices/salesReport/SalesReportPdfPrinterModel";

export class AdminSalesReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        orders: IOrderModel[] | undefined,
        orderSum: IOrderSumModel | undefined
    ) {
        const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();

        const model: ISalesReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: orders?.map((el) => ({
                invoiceNumber: el.invoiceNumber,
                orderType: el.deliveryApp ?? el.type,
                subTotal: el.subTotal,
                discount: el.discount,
                tobaccoTax: hasTobaccoTax ? el.tobaccoTax : undefined,
                vat: el.vat,
                total: el.total,
                cash: el.cash,
                network: el.network,
                credit: el.deliveryApp ? el.total : 0,
            })),
            totals: {
                subTotal: orderSum?.subTotal ?? 0,
                vat: orderSum?.vat ?? 0,
                total: orderSum?.total ?? 0,
                cash: orderSum?.cash ?? 0,
                network: orderSum?.network ?? 0,
                credit: orderSum?.deliveryApps ?? 0,
            },
        };
        PdfMakeUtils.preview(SalesReportPdfPrinterContent(model), { isLandScape: true });
    }
}