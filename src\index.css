@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
    font-family: "tajawal-extra-light";
    src: url("./common/assets/fonts/Tajawal-ExtraLight.ttf") format("truetype");
}

@font-face {
    font-family: "tajawal-light";
    src: url("./common/assets/fonts/Tajawal-Light.ttf") format("truetype");
}

@font-face {
    font-family: "tajawal-medium";
    src: url("./common/assets/fonts/Tajawal-Medium.ttf") format("truetype");
}

@font-face {
    font-family: "tajawal-regular";
    src: url("./common/assets/fonts/Tajawal-Regular.ttf") format("truetype");
}

@font-face {
    font-family: "tajawal-bold";
    src: url("./common/assets/fonts/Tajawal-Bold.ttf") format("truetype");
}

@font-face {
    font-family: "tajawal-extra-bold";
    src: url("./common/assets/fonts/Tajawal-ExtraBold.ttf") format("truetype");
}

@font-face {
    font-family: "tajawal-black";
    src: url("./common/assets/fonts/Tajawal-Black.ttf") format("truetype");
}

body {
    font-family: "tajawal-regular", sans-serif;
}

.h-s {
    height: 100vh;
    height: 100svh;
}

.w-s {
    width: 100vw;
    width: 100svw;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

input[type="number"] {
    -moz-appearance: textfield;
}

@layer utilities {
    @variants responsive {

        /* Hide scrollbar for Chrome, Safari and Opera */
        .no-scrollbar::-webkit-scrollbar {
            display: none;
        }

        /* Hide scrollbar for IE, Edge and Firefox */
        .no-scrollbar {
            -ms-overflow-style: none;
            /* IE and Edge */
            scrollbar-width: none;
            /* Firefox */
        }
    }
}

[dir="rtl"] .checkbox:checked,
[dir="rtl"] .checkbox[checked="true"],
[dir="rtl"] .checkbox[aria-checked="true"] {
    background-image: linear-gradient(-45deg,
            transparent 65%,
            hsl(var(--chkbg)) 65.99%),
        linear-gradient(45deg, transparent 75%, hsl(var(--chkbg)) 75.99%),
        linear-gradient(-45deg, hsl(var(--chkbg)) 40%, transparent 40.99%),
        linear-gradient(45deg,
            hsl(var(--chkbg)) 30%,
            hsl(var(--chkfg)) 30.99%,
            hsl(var(--chkfg)) 40%,
            transparent 40.99%),
        linear-gradient(-45deg, hsl(var(--chkfg)) 50%, hsl(var(--chkbg)) 50.99%);
}

body {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
}

.ltr-direction {
    direction: ltr;
}

input[type="datetime-local"] {
    width: 100%;
    /* Ensure it takes the full width */
    max-width: 100%;
    /* Prevent unwanted shrinkage */
    box-sizing: border-box;
    /* Includes padding/border in width */
    -webkit-appearance: none;
    /* Remove iOS native appearance */
    appearance: none;
    /* For cross-browser consistency */
    padding: 8px;
    /* Add some padding if needed */
}

.table tbody tr:hover td {
    background-color: #226ab210 !important;
}

::-webkit-scrollbar {
    width: 8px;
}

/* Track */
::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(128, 128, 128, 0.185);
    border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #555;
}