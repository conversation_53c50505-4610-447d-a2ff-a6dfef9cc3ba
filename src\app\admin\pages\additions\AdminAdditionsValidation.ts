import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IAdminAdditionInputs } from "./AdminAdditionsInterface";

export class AdminAdditionsValidation {
    static inputsValidation = (values: IAdminAdditionInputs) => {
        let isValid = true;

        if (!values.name) {
            isValid = false;
            ToastHelper.error(TranslateConstants.NAME_FIELD_IS_REQUIRED);
        } else if (values.price < 0 || values.price > 100000) {
            isValid = false;
            ToastHelper.error(TranslateConstants.PRICE_FIELD_MUST_BE_BETWEEN_0_AND_100000);
        }

        return isValid;
    };
}
