import { createSlice } from "@reduxjs/toolkit";
import InitialState from "./state";
import { closeModal, setLoading, setModal, setUpdatesCount } from "./actions";

export const AppSlice = createSlice({
    name: "app",
    initialState: InitialState,
    reducers: {
        setModalAction: setModal,
        closeModalAction: closeModal,
        setLoadingAction: setLoading,
        setUpdatesCountAction: setUpdatesCount,
    },
});

export const {
    setModalAction,
    closeModalAction,
    setLoadingAction,
    setUpdatesCountAction,
} = AppSlice.actions;

export default AppSlice.reducer;
