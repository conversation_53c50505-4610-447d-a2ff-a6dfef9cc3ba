import useFetch from "../../../../common/asyncController/useFetch";
import useFlatMutate from "../../../../common/asyncController/useFlatMutate";
import AddAndFilterComponent from "../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../common/components/StatusComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { EndPointsConstants } from "../../../../common/constants/ApiConstants";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { IDiscountModel } from "../../../../common/models/DiscountModel";
import useActions from "../../../../common/redux/data/useActions";
import { DiscountsApiRepo } from "../../../../common/repos/api/DiscountsApiRepo";
import { AdminDiscountDataTableHeaders } from "./AdminDiscountsConstants";
import AdminDiscountsModal from "./modals/AdminDiscountsModal";

const AdminDiscountsPage = () => {
    const actions = useActions();
    const { isSm } = useScreenSize();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsConstants.DISCOUNTS,
        DiscountsApiRepo.getDiscounts
    );

    const updateDiscount = useFlatMutate(DiscountsApiRepo.updateDiscount, {
        updateCached: {
            key: EndPointsConstants.DISCOUNTS,
            operation: "update",
            selector: (data: IDiscountModel) => data.id,
        },
    });

    const handleOnActive = (active: boolean, item: IDiscountModel) => 
        updateDiscount(item.id, { active });

    const handleOnEdit = (isEdit?: boolean, item?: IDiscountModel) => {
        actions.openModal({
            component: <AdminDiscountsModal item={item} />,
            title: isEdit ? TranslateConstants.EDIT : TranslateConstants.ADD,
            size: "md",
            showButtons: false,
        });
    };

    return (
        <>
            <AddAndFilterComponent onClick={handleOnEdit} onReload={refetch} />
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminDiscountDataTableHeaders}
                    items={data || []}
                    selectors={(item: IDiscountModel) => [
                        item.number,
                        item.name,
                        item.amount,
                    ]}
                    showEditButton={true}
                    onEdit={(item: IDiscountModel) => handleOnEdit(true, item)}
                    showActiveButton={true}
                    activeSelector={(item: IDiscountModel) => item.active}
                    onActive={handleOnActive}
                />
            </StatusComponent>
        </>
    );
};

export default AdminDiscountsPage;
