import { useDispatch } from "react-redux";
import { AppDispatch } from "../redux/store";
import { closeModalAction, setLoadingAction } from "../redux/data/slice";
import { ToastHelper } from "../helpers/ToastHelper";
import { debug } from "../utils/CommonUtils";
import { TranslateHelper } from "../helpers/TranslateHelper";
import { updateCachedData } from "./slice";
import { TranslateConstants } from "../constants/TranslateConstants";
import useOnlineStatus from "../hooks/useOnlineStatus";

type UpdateCachedAdd = {
    key: string;
    operation: "add" | "updateOne";
};

type UpdateCachedUpdateOrDelete = {
    key: string;
    operation: "update" | "delete";
    selector: (data: any) => any;
};

interface UseMutateOptions<T> {
    onSuccess?: (params: { data: T; dispatch: AppDispatch; args: any[] }) => void;
    onError?: (params: {
        error: string;
        dispatch: AppDispatch;
        args: any[];
    }) => void;
    beforeStart?: (params: { dispatch: AppDispatch; args: any[] }) => void;
    afterEnd?: (params: { dispatch: AppDispatch; args: any[] }) => void;
    onErrorToast?: string;
    onSuccessToast?: string;
    isShowLoadingPage?: boolean;
    closeModalOnSuccess?: boolean;
    showDefaultErrorToast?: boolean;
    showDefaultSuccessToast?: boolean;
    isOnline?: boolean;
    updateCached?: UpdateCachedAdd | UpdateCachedUpdateOrDelete;
}

const useFlatMutate = <T>(
    mutateFn: (...args: any[]) => Promise<T>,
    options: UseMutateOptions<T> = {}
) => {
    const {
        onSuccess,
        onError,
        beforeStart,
        afterEnd,
        onErrorToast,
        onSuccessToast,
        isShowLoadingPage = true,
        closeModalOnSuccess = false,
        showDefaultErrorToast = true,
        showDefaultSuccessToast = false,
        isOnline = false,
        updateCached,
    } = options;

    const dispatch = useDispatch<AppDispatch>();
    const onlineState = useOnlineStatus();

    const mutate = async (...args: any[]) => {
        try {
            if (isOnline && !onlineState) {
                ToastHelper.error(
                    TranslateHelper.t(TranslateConstants.ERROR_NO_INTERNET_CONNECTION)
                );
                return;
            }

            if (isShowLoadingPage) dispatch(setLoadingAction(true));
            if (beforeStart) beforeStart({ dispatch, args });

            const result = await mutateFn(...args);
            onSuccess?.({ data: result, dispatch, args });

            if (updateCached) {
                dispatch(updateCachedData({ ...updateCached, result }));
            }

            if (onSuccessToast)
                ToastHelper.success(TranslateHelper.t(onSuccessToast));
            else if (showDefaultSuccessToast)
                ToastHelper.success(
                    TranslateHelper.t(TranslateConstants.SUCCESS_TOAST)
                );
            if (closeModalOnSuccess) dispatch(closeModalAction());
        } catch (err: any) {
            if (onErrorToast) ToastHelper.error(TranslateHelper.t(onErrorToast));
            else if (showDefaultErrorToast)
                ToastHelper.error(
                    TranslateHelper.t(TranslateConstants.ERROR_PLEASE_TRY_AGAIN)
                );

            onError?.({ error: err.message, dispatch, args });
            debug(`Error in useMutate: `, err.message);
        } finally {
            if (isShowLoadingPage) dispatch(setLoadingAction(false));
            if (afterEnd) afterEnd({ dispatch, args });
        }
    };

    return mutate;
};

export default useFlatMutate;
