import { EndPointsConstants } from "../../constants/ApiConstants";
import { DeliveryAppEnum } from "../../enums/DataEnums";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IOrderModel } from "../../models/OrderModel";
import { IOrderSumModel } from "../../models/OrderSumModel";
import { debug } from "../../utils/CommonUtils";

export class OrdersApiRepo {
    static async getSum(
        startTime: number,
        endTime: number,
        customerId?: number,
        deliveryApp?: DeliveryAppEnum
    ) {
        try {
            const res = await AxiosHelper.get<IOrderSumModel>(
                EndPointsConstants.ORDERS_SUM,
                { params: { startTime, endTime, customerId, deliveryApp } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`OrdersApiRepo [getSum] Error: `, error);
            throw error;
        }
    }

    static async getOrders(
        startTime?: number,
        endTime?: number,
        customerId?: number,
        getProducts?: boolean,
        deliveryApp?: DeliveryAppEnum
    ) {
        try {
            const res = await AxiosHelper.get<IOrderModel[]>(
                EndPointsConstants.ORDERS,
                { params: { startTime, endTime, customerId, getProducts, deliveryApp } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`OrdersApiRepo [getOrders] Error: `, error);
            throw error;
        }
    }
}
