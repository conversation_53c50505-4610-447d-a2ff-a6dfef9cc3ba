import { FC } from "react";
import SpineComponent from "./SpineComponent";
import ErrorComponent from "./ErrorComponent";
import EmptyComponent from "./EmptyComponent";

interface IProps {
    children: React.ReactNode;
    className?: string;
    height?: number;
    isLoading?: boolean;
    isError?: boolean;
    isEmpty?: boolean;
}

const StatusComponent: FC<IProps> = ({
    children,
    className = "",
    height = 11.35,
    isLoading = false,
    isError = false,
    isEmpty = false,
}) => {
    return (
        <div
            className={"border p-2 rounded-lg bg-base-100 border-slate-400" + " " + className}
            style={{ height: `calc(100svh - ${height}rem)` }}
        >
            {isLoading && <SpineComponent />}
            {isError && !isLoading && <ErrorComponent />}
            {isEmpty && !isLoading && !isError && <EmptyComponent />}
            {!isEmpty && !isLoading && !isError && children}
        </div>
    );
};

export default StatusComponent;
