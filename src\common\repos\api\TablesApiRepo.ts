import { IAdminTableInputs } from "../../../app/admin/pages/tables/AdminTablesInterface";
import { EndPointsConstants } from "../../constants/ApiConstants";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { ITableModel } from "../../models/TableModel";
import { debug } from "../../utils/CommonUtils";

export class TablesApiRepo {
    static async getTables() {
        try {
            const res = await AxiosHelper.get<ITableModel[]>(EndPointsConstants.TABLES);
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`TablesApiRepo [getTables] Error: `, error);
            throw error;
        }
    }

    static async addTable(body: IAdminTableInputs) {
        try {
            const res = await AxiosHelper.post<ITableModel>(EndPointsConstants.TABLES, body);
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`TablesApiRepo [addTable] Error: `, error);
            throw error;
        }
    }

    static async updateTable(id: number, body: IAdminTableInputs) {
        try {
            const res = await AxiosHelper.patch<ITableModel>(EndPointsConstants.TABLES, id, body);
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`TablesApiRepo [updateTable] Error: `, error);
            throw error;
        }
    }
}