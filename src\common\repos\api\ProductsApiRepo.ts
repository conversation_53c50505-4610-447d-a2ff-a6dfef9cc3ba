import { IAdminProductInputs } from "../../../app/admin/pages/products/AdminProductsInterface";
import { uploadImage } from "../../config/firebase";
import { EndPointsConstants } from "../../constants/ApiConstants";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IProductBody } from "../../interfaces/body/ProductBody";
import { IProductModel } from "../../models/ProductModel";
import { debug } from "../../utils/CommonUtils";

export class ProductsApiRepo {
    static async getProducts(
        active?: boolean,
        deliveryApps?: boolean,
        excludeTobaccoTax?: boolean
    ) {
        try {
            const res = await AxiosHelper.get<IProductModel[]>(
                EndPointsConstants.PRODUCTS,
                { params: { active, deliveryApps, excludeTobaccoTax } }
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`ProductsApiRepo [getProducts] Error: `, error);
            throw error;
        }
    }

    static async addProduct(inputs: IAdminProductInputs) {
        try {
            const body: IProductBody = {
                name: inputs.name || "",
                price: inputs.price || 0,
                image: "",
                isIncludingAdditions: inputs.isIncludingAdditions,
                isSubjectToTobaccoTax: inputs.isSubjectToTobaccoTax || false,
                categoryId: inputs.categoryId || 0,
                productSizeType: inputs.productSizeType,
                active: inputs.active,
                sizes: inputs.sizes || [],
            };
            if (inputs.image) body.image = await uploadImage(inputs.image);
            const res = await AxiosHelper.post<IProductModel>(
                EndPointsConstants.PRODUCTS,
                body
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`ProductsApiRepo [addProduct] Error: `, error);
            throw error;
        }
    }

    static async updateProduct(id: number, inputs: IAdminProductInputs) {
        try {
            const body: Partial<IProductBody> = {
                name: inputs.name,
                price: inputs.price,
                isIncludingAdditions: inputs.isIncludingAdditions,
                isSubjectToTobaccoTax: inputs.isSubjectToTobaccoTax,
                categoryId: inputs.categoryId,
                productSizeType: inputs.productSizeType,
                sizes: inputs.sizes?.map((e) => ({
                    id: e.item?.id,
                    name: e.name,
                    price: e.price,
                })),
                active: inputs.active,
            };
            if (inputs.image) body.image = await uploadImage(inputs.image);
            const res = await AxiosHelper.patch<IProductModel>(
                EndPointsConstants.PRODUCTS,
                id,
                body
            );
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`ProductsApiRepo [updateProduct] Error: `, error);
            throw error;
        }
    }
}
