import {
    APP_LOCAL_DB_COLLECTIONS,
    AppLocalDB,
} from "../../../common/config/localDB";
import { EndPointsConstants } from "../../../common/constants/ApiConstants";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { IProductModel } from "../../../common/models/ProductModel";
import { debug } from "../../../common/utils/CommonUtils";

export class PosProductsRepo {
    static async getAndCacheProducts(updatedAt?: Date) {
        try {
            const res = await AxiosHelper.get<IProductModel[]>(
                EndPointsConstants.PRODUCTS,
                { params: { updatedAt, deliveryApps: true } }
            );

            if (!res.success) throw new Error(res.message);
            const data = res.data || [];

            if (!updatedAt) await AppLocalDB.set(APP_LOCAL_DB_COLLECTIONS.PRODUCTS, data);
            else await AppLocalDB.add(APP_LOCAL_DB_COLLECTIONS.PRODUCTS, data, true);
        } catch (error) {
            debug(`PosProductsRepo [getAndCacheProducts] Error: `, error);
            throw error;
        }
    }

    static async getProducts(activeOnly: boolean = false) {
        try {
            const res = await AppLocalDB.get<IProductModel>(
                APP_LOCAL_DB_COLLECTIONS.PRODUCTS,
                { where: activeOnly ? [[["active", "==", true]]] : undefined }
            );

            if (!res) {
                throw new Error("No products found");
            }

            return { data: res };
        } catch (error) {
            debug(`PosProductsRepo [getProducts] Error: `, error);
            throw error;
        }
    }
}
