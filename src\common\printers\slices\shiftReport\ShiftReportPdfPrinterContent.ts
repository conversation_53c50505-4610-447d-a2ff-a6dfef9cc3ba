
import { Content } from "pdfmake/interfaces";
import { IShiftReportPdfPrinterModel } from "./ShiftReportPdfPrinterModel";
import { DateUtils } from "../../../utils/DateUtils";
import { RsSvg } from "../../../assets/svgs/RsSvg";
import { OrganizationHelper } from "../../../helpers/OrganizationHelper";
import { PdfMakeHelper } from "../../PdfMakeHelper";

export const ShiftReportPdfPrinterContent = (model: IShiftReportPdfPrinterModel): Content[] => {
    const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();

    return [
        {
            text: "تقرير الوردية", style: { fontSize: 14, bold: true },
        },
        {
            text: DateUtils.format(model.endDate, "dd-MM-yyyy") + " - " + DateUtils.format(model.startDate, "dd-MM-yyyy"),
            margin: [0, 10, 0, 10],
        },
        {
            style: "table",
            table: {
                headerRows: 1,
                widths: [
                    "auto",
                    "auto",
                    "*",
                    "auto",
                    "auto",
                    "auto",
                    "auto",
                    ...(hasTobaccoTax ? ["auto"] : []),
                    "auto",
                    "*",
                    "*",
                    "auto",
                ],
                body: [
                    [
                        { text: "العجز", style: "tableHeader" },
                        { text: "الزيادة", style: "tableHeader" },
                        { text: "تطبيقات التوصيل", style: "tableHeader", fontSize: 10 },
                        { text: "الشبكة", style: "tableHeader" },
                        { text: "نقداً", style: "tableHeader" },
                        { text: "الإجمالي", style: "tableHeader" },
                        { text: "الضريبة", style: "tableHeader" },
                        ...PdfMakeHelper.optionalDocItem(hasTobaccoTax,
                            [{ text: "ضريبةالتبغ", style: "tableHeader" }]
                        ),
                        { text: "الخصم", style: "tableHeader" },
                        { text: "مبلغ النهاية", style: "tableHeader" },
                        { text: "مبلغ البداية", style: "tableHeader" },
                        { text: "الطلبات", style: "tableHeader" },
                    ],
                    ...(model.items || []).map((el) => [
                        el.shortageAmount.toFixed(2),
                        el.additionAmount.toFixed(2),
                        el.deliveryAppsAmount.toFixed(2),
                        el.networkAmount.toFixed(2),
                        el.cashAmount.toFixed(2),
                        el.totalAmount.toFixed(2),
                        el.vatAmount.toFixed(2),
                        ...PdfMakeHelper.optionalDocItem(hasTobaccoTax,
                            [el.tobaccoTaxAmount?.toFixed(2) || "0"]
                        ),
                        el.discountAmount.toFixed(2),
                        el.endAmount.toFixed(2),
                        el.startAmount.toFixed(2),
                        el.ordersCount.toString(),
                    ]),
                ],
            },
        },
        {
            margin: [0, 20, 10, 20],
            alignment: "right",
            bold: true,
            fontSize: 12,
            color: "#1F618D",
            stack: [
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.ordersCount.toFixed(2), margin: [0, 4, 0, 0] },
                                        { text: "عدد الطلبات:", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.vatAmount.toFixed(2), margin: [0, 4, 0, 0] },
                                        { text: "الضريبة: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.totalAmount.toFixed(2), margin: [0, 4, 0, 0] },
                                        { text: "الإجمالي:", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.additionAmount.toFixed(2), margin: [0, 4, 0, 0] },
                                        { text: "الزيادة: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.shortageAmount.toFixed(2), margin: [0, 4, 0, 0] },
                                        { text: "العجز: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
            ],
        }
    ];
}