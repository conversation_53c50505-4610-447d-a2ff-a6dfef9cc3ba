import { useState } from "react";

type State = string | number | boolean | object | any[];

const handleInitialState = (
    initialState: any,
    updateState?: any,
    explodeFromUpdate: any[] = []
) => {
    if (updateState) {
        if (typeof initialState === "object" && typeof updateState === "object") {
            const initKeys = Object.keys(initialState);
            const updateKeys = Object.keys(updateState);
            const res = { ...initialState };

            for (let key of initKeys) {
                if (updateKeys.includes(key) && !explodeFromUpdate.includes(key)) {
                    res[key] = updateState[key];
                }
            }

            return res;
        }

        return updateState;
    }
    return initialState;
};

const useCustomState = <T>(
    initialState: State,
    updateState?: State,
    explodeFromReset: (keyof T)[] = [],
    explodeFromUpdate: (keyof T)[] = []
): [T, (value: T) => void, () => void] => {
    const [state, setState] = useState<T>(
        handleInitialState(initialState, updateState, explodeFromUpdate)
    );

    const resetState = () => {
        if (explodeFromReset.length && typeof initialState === "object") {
            setState((prev: T) => {
                const resetState = { ...initialState } as T;
                for (let key of explodeFromReset) {
                    (resetState as any)[key] = (prev as any)[key];
                }
                return resetState;
            });
            return;
        }
        setState(handleInitialState(initialState));
    };

    return [state, setState, resetState];
};

export default useCustomState;
