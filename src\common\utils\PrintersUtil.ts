import { IPosOrder } from "../../app/pos/interface";
import { TranslateConstants } from "../constants/TranslateConstants";
import { KitchenOrderStatusEnum, OrderTypeEnum } from "../enums/DataEnums";
import { OrganizationHelper } from "../helpers/OrganizationHelper";
import { PrintersHelper } from "../helpers/PrintersHelper";
import { QrHelper } from "../helpers/QrHelper";
import { TranslateHelper } from "../helpers/TranslateHelper";
import { IKitchenPrinter } from "../interfaces";
import { IInvoicePrinterItemModel } from "../models/InvoicePrinterItemModel";
import { IInvoicePrinterModel } from "../models/InvoicePrinterModel";
import { IKitchenPrinterItemModel } from "../models/KitchenPrinterItemModel";
import { IKitchenPrinterModel } from "../models/KitchenPrinterModel";
import { getOrderType } from "./CommonUtils";
import { fixedNumber } from "./numberUtils";
import { orderUtils } from "./OrderUtils";

export class PrintersUtils {
    static handleInvoiceOrderPrint(order: IPosOrder): IInvoicePrinterModel {
        const organization = OrganizationHelper.getOrganization();
        const hasVat = OrganizationHelper.hasVat();
        const printerId = PrintersHelper.getDefaultPrinter()?.printer;
        const invoiceTitle = TranslateHelper.t(
            hasVat
                ? TranslateConstants.SIMPLE_VAT_INVOICE
                : TranslateConstants.SIMPLE_INVOICE
        );

        if (!printerId) throw new Error("Printer not found");

        const items: IInvoicePrinterItemModel[] = order.products.map((el) => {
            return {
                product: el.name,
                price: (el.price * el.quantity).toFixed(2),
                quantity: fixedNumber(el.price) + " x " + fixedNumber(el.quantity),
                additions: el.additions?.map((ad) => ({
                    name: ad.name,
                    price: (ad.price * ad.quantity * el.quantity).toFixed(2),
                    quantity:
                        fixedNumber(ad.price) +
                        " x " +
                        fixedNumber(ad.quantity * el.quantity),
                })),
            };
        });

        const { subTotal, vat, total, discount, tobaccoTax } =
            orderUtils.getPosOrderData(order);

        return {
            printerId,
            body: {
                organizationName: organization?.name || "",
                organizationSubName: organization?.subName || "",
                orderType: getOrderType(order),
                invoiceNumber: order.invoiceNumber || "",
                orderNumber: order.orderNumber || "",
                items,
                invoiceTitle,
                address: organization?.address || "",
                vatNo: organization?.taxNumber || "",
                crNo: organization?.registrationNumber || "",
                phone: organization?.mobile || "",
                qrCode: QrHelper.vatQrData(total, vat),
                subTotal: subTotal.toFixed(2),
                discount: discount.toFixed(2),
                vat: vat.toFixed(2),
                total: total.toFixed(2),
                cash: order.cash.toFixed(2),
                network: order.network.toFixed(2),
                totalDeliverApp:
                    order.type === OrderTypeEnum.DELIVERY_APP
                        ? order.total.toFixed(2)
                        : "",
                tobaccoTax: tobaccoTax.toFixed(2),
                customerName: order.customer?.name || "",
                customerMobile: order.customer?.mobile || "",
                customerTaxNumber: order.customer?.taxNumber || "",
                customerAddress: order.customer?.address || "",
                footer: organization?.invoiceFooter || "",
            },
        };
    }

    static handelKitchenOrderPrint(
        order: IPosOrder,
        orderStatus: KitchenOrderStatusEnum
    ): IKitchenPrinterModel[] {
        const kitchenPrinterItems = this.handleKitchenPrinterItems(order, orderStatus);
        const organization = OrganizationHelper.getOrganization();
        const result: IKitchenPrinterModel[] = [];

        kitchenPrinterItems.forEach((item) => {
            result.push({
                printerId: item.printerId,
                body: {
                    items: item.products,
                    organizationName: organization?.name || "",
                    orderNumber: order.orderNumber || "",
                    orderType: TranslateHelper.t(order.deliveryApp ?? order.type),
                    table: order.table?.name || "",
                    orderTitle: TranslateHelper.t(orderStatus),
                },
            });
        });

        return result;
    }

    private static handleKitchenPrinterItems(
        order: IPosOrder,
        orderStatus: KitchenOrderStatusEnum
    ): IKitchenPrinter[] {
        const result: IKitchenPrinter[] = [];
        const printers = PrintersHelper.getPrinters();

        order.products.forEach((el) => {
            const printer = printers.find(
                (p) => p.categoryId === el.product.categoryId.toString()
            );
            if (!printer?.printer) return;

            const kitchenOrder = result.find(
                (ko) => ko.printerId === printer.printer
            );
            const isDeleted = orderStatus === KitchenOrderStatusEnum.DELETE || el.isDeleted;

            const item: IKitchenPrinterItemModel = {
                product: el.name,
                quantity: (isDeleted ? "-" : "") + fixedNumber(el.quantity).toString(),
                additions: el.additions?.map((ad) => ({
                    name: ad.name,
                    quantity: fixedNumber(ad.quantity).toString(),
                })),
                isDeleted
            };

            if (kitchenOrder) {
                kitchenOrder.products.push(item);
            } else {
                result.push({
                    printerId: printer.printer,
                    products: [item],
                });
            }
            return;
        });

        return result;
    }
}
