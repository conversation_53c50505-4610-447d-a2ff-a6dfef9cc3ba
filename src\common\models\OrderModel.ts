import { DeliveryAppEnum, OrderStatusEnum, OrderTypeEnum } from "../enums/DataEnums";

export interface IOrderProductModel {
    productId: number;
    quantity: number;
    price: number;
    name: string;
    subTotal: number;
    discount: number;
    tobaccoTax: number;
    vat: number;
    total: number;
    startTime: number;
    isSubjectToTobaccoTax: boolean;
}

export interface IOrderModel {
    shiftId: number;
    type: OrderTypeEnum;
    deliveryApp?: DeliveryAppEnum;
    products: IOrderProductModel[];
    tableId?: number;
    selectedDiscountId?: number;
    status: OrderStatusEnum;
    invoiceNumber: string;
    orderNumber: string;
    subTotal: number;
    discount: number;
    tobaccoTax: number;
    vat: number;
    total: number;
    deliveryAppFee: number;
    totalDue: number;
    cash: number;
    network: number;
    startTime: number;
    endTime: number;
}