import useFetch from "../../../../common/asyncController/useFetch";
import useFlatMutate from "../../../../common/asyncController/useFlatMutate";
import AddAndFilterComponent from "../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../common/components/StatusComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { EndPointsConstants } from "../../../../common/constants/ApiConstants";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { ITableModel } from "../../../../common/models/TableModel";
import useActions from "../../../../common/redux/data/useActions";
import { TablesApiRepo } from "../../../../common/repos/api/TablesApiRepo";
import { AdminTableDataTableHeaders } from "./AdminTablesConstants";
import AdminTablesModal from "./modals/AdminTablesModal";

const AdminTablesPage = () => {
    const actions = useActions();
    const { isSm } = useScreenSize();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsConstants.TABLES,
        TablesApiRepo.getTables
    );

    const updateTable = useFlatMutate(TablesApiRepo.updateTable, {
        updateCached: {
            key: EndPointsConstants.TABLES,
            operation: "update",
            selector: (data: ITableModel) => data.id,
        },
    });

    const handleOnActive = (active: boolean, item: ITableModel) =>
        updateTable(item.id, { active });

    const handleOnClick = (isEdit?: boolean, item?: ITableModel) => {
        actions.openModal({
            component: <AdminTablesModal isEdit={isEdit} item={item} />,
            title: isEdit ? TranslateConstants.EDIT : TranslateConstants.ADD,
            size: "md",
            showButtons: false,
        });
    };

    return (
        <>
            <AddAndFilterComponent onClick={handleOnClick} onReload={refetch} />
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminTableDataTableHeaders}
                    items={data || []}
                    selectors={(item: ITableModel) => [item.number, item.name]}
                    showEditButton={true}
                    showDeleteButton={false}
                    onEdit={(item: ITableModel) => handleOnClick(true, item)}
                    showActiveButton={true}
                    activeSelector={(item: ITableModel) => item.active}
                    onActive={handleOnActive}
                />
            </StatusComponent>
        </>
    );
};

export default AdminTablesPage;
