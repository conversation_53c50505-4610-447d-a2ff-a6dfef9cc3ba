import { IPosOrder } from "../../app/pos/interface";

export class EqualityUtils {
    static isOrderEqual(order: IPosOrder, updatedOrder: IPosOrder) {
        let isTableEqual = true;
        let isProductsEqual = true;

        if (order.table?.id !== updatedOrder.table?.id) {
            isTableEqual = false;
        }

        if (order.products.length !== updatedOrder.products.length) {
            isProductsEqual = false;
        } else {
            for (let i = 0; i < order.products.length; i++) {
                if (
                    order.products[i].product.id !==
                    updatedOrder.products[i].product.id ||
                    order.products[i].quantity !== updatedOrder.products[i].quantity
                ) {
                    isProductsEqual = false;
                    break;
                }
            }
        }

        return { isProductsEqual, isTableEqual };
    }
}
