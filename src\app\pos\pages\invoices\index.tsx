import StatusComponent from "../../../../common/components/StatusComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { OrderStatusEnum } from "../../../../common/enums/DataEnums";
import { useTranslate } from "../../../../common/hooks/useTranslate";
import { IPosOrderModel } from "../../../../common/models/PosOrderModel";
import { useGetPosOrdersQuery } from "../../../../common/redux/api/slice";
import useActions from "../../../../common/redux/data/useActions";
import { getOrderType } from "../../../../common/utils/CommonUtils";
import { DateUtils } from "../../../../common/utils/DateUtils";
import PosBackButtonComponent from "../../components/PosBackButtonComponent";
import { PosInvoicesTableHeaderConstants } from "./PosInvoicesConstants";
import { PosInvoicesService } from "./PosInvoicesService";

const PosInvoicesPage = () => {
    const actions = useActions();
    const { translate } = useTranslate();
    const { data, isLoading, isError, isFetching } = useGetPosOrdersQuery(
        undefined,
        {
            refetchOnMountOrArgChange: true,
        }
    );

    const handleOnPrint = async (order: IPosOrderModel) =>
        PosInvoicesService.handleOnPrint(actions, order);

    return (
        <div className="p-4 flex flex-col gap-2">
            <div className="font-tajawal-bold text-2xl">
                {translate(TranslateConstants.INVOICES)}
            </div>
            <StatusComponent
                isLoading={isLoading || isFetching}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={12}
            >
                <TableComponent
                    headers={PosInvoicesTableHeaderConstants}
                    items={data || []}
                    selectors={(item: IPosOrderModel) => {
                        const statusColor = () => {
                            if (item.status === OrderStatusEnum.PENDING) return "text-yellow-600";
                            else if (item.status === OrderStatusEnum.COMPLETED) return "text-green-600";
                            else if (item.status === OrderStatusEnum.CANCELLED) return "text-red-600";
                            return ""
                        }

                        return [
                            item.invoiceNumber,
                            item.orderNumber,
                            getOrderType(item),
                            item.table?.name,
                            item.customer?.name,
                            item.customer?.mobile,
                            item.cash.toFixed(2),
                            item.network.toFixed(2),
                            item.total.toFixed(2),
                            DateUtils.format(item.createdAt),
                            <div className={statusColor()}>{translate(item.status)}</div>,
                        ]
                    }}
                    showPrintButton={true}
                    onPrint={handleOnPrint}
                />
            </StatusComponent>
            <PosBackButtonComponent />
        </div>
    );
};

export default PosInvoicesPage;
