import { FC } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { IShiftSumModel } from "../../../../../common/models/ShiftsSumModel";

interface IProps {
    shiftSum?: IShiftSumModel;
}

const AdminShiftsReportTotalInfoFeature: FC<IProps> = ({ shiftSum }) => {
    return (
        <ReportTotalInfoComponent
            items={[
                { text: TranslateConstants.ORDERS_COUNT, value: shiftSum?.ordersCount, fixedVal: 0 },
                { text: TranslateConstants.TAX, value: shiftSum?.vatAmount },
                { text: TranslateConstants.TOTAL, value: shiftSum?.totalAmount },
                {
                    text: TranslateConstants.ADDITION,
                    value: shiftSum?.additionAmount,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.SHORTAGE,
                    value: shiftSum?.shortageAmount,
                    isHiddenInSm: true,
                },
            ]}
        />
    );
};

export default AdminShiftsReportTotalInfoFeature;
