export interface IAdminSettingsInputs {
    name?: string;
    nameEn?: string;
    subName?: string;
    subNameEn?: string;
    taxNumber?: string;
    registrationNumber?: string;
    mobile?: string;
    address?: string;
    password?: string;
    logo?: File | null;
    adminPassword?: string;
    posPassword?: string;
    activateTobaccoTax?: boolean
    invoiceFooter?: string;
}

export interface IAdminSettingsFormattedData {
    name?: string;
    nameEn?: string;
    subName?: string;
    subNameEn?: string;
    taxNumber?: string;
    registrationNumber?: string;
    mobile?: string;
    address?: string;
    password?: string;
    logo?: string;
}