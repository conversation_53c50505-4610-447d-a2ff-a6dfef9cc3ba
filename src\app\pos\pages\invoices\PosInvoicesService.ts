import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { OrderStatusEnum } from "../../../../common/enums/DataEnums";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IPosOrderModel } from "../../../../common/models/PosOrderModel";
import { IActions } from "../../../../common/redux/data/useActions";
import { debug } from "../../../../common/utils/CommonUtils";
import { PosPrintersRepo } from "../../repo/PosPrintersRepo";

export class PosInvoicesService {
    static async handleOnPrint(
        actions: IActions,
        order: IPosOrderModel
    ) {
        try {
            actions.setLoading();
            if (order.status !== OrderStatusEnum.COMPLETED) {
                ToastHelper.error(TranslateConstants.ERROR_PRINT_INVOICE_ORDER_STATUS);
                return
            }
            await PosPrintersRepo.printInvoiceOrder(order);
        } catch (error) {
            debug(`PosInvoicesService [handleOnPrint] Error: ${error}`);
            ToastHelper.error(TranslateConstants.ERROR_PRINT_INVOICE_ORDER);
        } finally {
            actions.setLoading(false);
        }
    }
}