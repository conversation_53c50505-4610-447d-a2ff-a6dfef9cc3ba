import ButtonComponent from "../../../common/components/ButtonComponent";
import { TranslateConstants } from "../../../common/constants/TranslateConstants";
import useOnlineStatus from "../../../common/hooks/useOnlineStatus";
import { IoSync } from "react-icons/io5";
import { IoWarningOutline } from "react-icons/io5";
import { useAppSelector } from "../../../common/redux/store";
import { updatesCountSelector } from "../../../common/redux/data/selector";
import { useEffect, useState } from "react";
import { OrganizationHelper } from "../../../common/helpers/OrganizationHelper";
import useActions from "../../../common/redux/data/useActions";
import { PosCategoriesRepo } from "../repo/PosCategoriesRepo";
import { PosProductsRepo } from "../repo/PosProductsRepo";
import { PosTablesRepo } from "../repo/PosTablesRepo";
import { PosLayoutService } from "../layout/PosLayoutService";
import usePosActions from "../redux/usePosActions";
import { ToastHelper } from "../../../common/helpers/ToastHelper";
import { PosDiscountsRepo } from "../repo/PosDiscountsRepo";
import { PosCustomersRepo } from "../repo/PosCustomersRepo";
import { PosAdditionsRepo } from "../repo/PosAdditionsRepo";
import { PosDeliveryAppsRepo } from "../repo/PosDeliveryAppsRepo";

const PosSyncButtonComponent = () => {
    const actions = useActions();
    const posActions = usePosActions();
    const isOnline = useOnlineStatus();
    const updatesCount = useAppSelector(updatesCountSelector);
    const [needToUpdate, setNeedToUpdate] = useState(false)

    useEffect(() => {
        setNeedToUpdate(OrganizationHelper.needToUpdate(updatesCount));
    }, [updatesCount]);

    const handleOnClick = async () => {
        actions.setLoading();
        const {
            categoriesUpdatesCount,
            productsUpdatesCount,
            tablesUpdatesCount,
            discountsUpdatesCount,
            customersUpdatesCount,
            additionsUpdatesCount,
            deliveryAppsUpdatesCount,
            lastPosUpdateAt,
        } = OrganizationHelper.getUpdateMetaData();

        if (categoriesUpdatesCount !== updatesCount.categoriesUpdatesCount)
            await PosCategoriesRepo.getAndCacheCategories(lastPosUpdateAt);

        if (productsUpdatesCount !== updatesCount.productsUpdatesCount)
            await PosProductsRepo.getAndCacheProducts(lastPosUpdateAt);

        if (tablesUpdatesCount !== updatesCount.tablesUpdatesCount)
            await PosTablesRepo.getAndCacheTables(lastPosUpdateAt);

        if (discountsUpdatesCount !== updatesCount.discountsUpdatesCount)
            await PosDiscountsRepo.getAndCacheDiscounts(lastPosUpdateAt);

        if (customersUpdatesCount !== updatesCount.customersUpdatesCount)
            await PosCustomersRepo.getAndCacheCustomers(lastPosUpdateAt);

        if (additionsUpdatesCount !== updatesCount.additionsUpdatesCount)
            await PosAdditionsRepo.getAndCacheAdditions(lastPosUpdateAt);

        if (deliveryAppsUpdatesCount !== updatesCount.deliveryAppsUpdatesCount)
            await PosDeliveryAppsRepo.getAndCacheDeliveryApps();

        OrganizationHelper.setUpdatesMetaData(updatesCount);
        await PosLayoutService.getAllData(posActions, actions);
        setNeedToUpdate(false);
        actions.setLoading(false);
        ToastHelper.success(TranslateConstants.SYNC_SUCCESSFULLY);
    };

    return (
        <ButtonComponent
            text={TranslateConstants.SYNC}
            iconComponent={
                isOnline ? (
                    <IoSync className="text-xl" />
                ) : (
                    <IoWarningOutline className="text-xl text-red-500" />
                )
            }
            bgColor="base-200"
            isCentered={false}
            className="flex-row-reverse py-3 rounded-lg"
            textColor="black"
            isBorder={isOnline && needToUpdate}
            isDisabled={!isOnline || !needToUpdate}
            onClick={handleOnClick}
        />
    );
};

export default PosSyncButtonComponent;
