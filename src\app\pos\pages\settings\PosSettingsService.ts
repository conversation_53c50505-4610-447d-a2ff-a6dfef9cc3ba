import { Dispatch, SetStateAction } from "react";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IActions } from "../../../../common/redux/data/useActions";
import { PosPrintersRepo } from "../../repo/PosPrintersRepo";
import { IPrinterModel } from "../../../../common/models/PrinterModel";
import { PrintersHelper } from "../../../../common/helpers/PrintersHelper";
import { IPrinterData } from "../../../../common/interfaces";
import { debug, isOnlineState } from "../../../../common/utils/CommonUtils";
import { ICategoryModel } from "../../../../common/models/CategoryModel";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { PosReceiptPrinterConstant } from "./PosSettingsConstants";
import { OrganizationRepo } from "../../../admin/repos/OrganizationRepo";

export class PosSettingsService {
    static async init(
        actions: IActions,
        setPrinters: Dispatch<SetStateAction<IPrinterModel[]>>
    ) {
        try {
            actions.setLoading(true);
            await this.getAllPrinters(setPrinters);
            await this.checkUpdates(actions);
        } catch (error) {
            debug("PosSettingsService [init]", error);
        } finally {
            actions.setLoading(false);
        }
    }

    static async checkUpdates(actions: IActions) {
        try {
            if (!isOnlineState) return;
            const updatesCount = await OrganizationRepo.getUpdatesCount();
            actions.setUpdatesCount(updatesCount);
            return updatesCount;
        } catch (error) {
            debug("PosSettingsService [checkUpdates]", error);
            ToastHelper.error();
        }
    }

    static async getAllPrinters(
        setPrinters: Dispatch<SetStateAction<IPrinterModel[]>>
    ) {
        try {
            const res = await PosPrintersRepo.getAllPrinters();
            setPrinters(res);
        } catch (error) {
            debug("PosSettingsService [getAllPrinters]", error);
            ToastHelper.error(TranslateConstants.ERROR_GETTING_PRINTERS);
        }
    }

    static onSave(
        data: IPrinterData[],
        setDisabled: Dispatch<SetStateAction<boolean>>
    ) {
        PrintersHelper.setPrinters(data);
        ToastHelper.success();
        setDisabled(true);
    }

    static handleOnPrinterSelect(
        setData: Dispatch<SetStateAction<IPrinterData[]>>,
        index: number,
        value: IPrinterModel
    ) {
        setData((prev) => {
            return prev.map((item, i) => {
                if (i === index) return { ...item, printer: value.deviceId };
                return item;
            });
        });
    }

    static async initPrintersData(
        setData: Dispatch<SetStateAction<IPrinterData[]>>,
        categories: ICategoryModel[]
    ) {
        const savedData = PrintersHelper.getPrinters();
        const receiptPrinterData = this.handleReceiptPrinterInit(savedData);
        const printerData = this.handlePrintersCategoryInit(savedData, categories);

        setData([receiptPrinterData, ...printerData]);
    }

    private static handleReceiptPrinterInit(
        savedData: IPrinterData[]
    ): IPrinterData {
        const receiptId = PosReceiptPrinterConstant.categoryId;
        const receiptPrinter = savedData.find((el) => el.categoryId === receiptId);

        return {
            ...PosReceiptPrinterConstant,
            printer: receiptPrinter?.printer,
        };
    }

    private static handlePrintersCategoryInit(
        savedData: IPrinterData[],
        categories: ICategoryModel[]
    ): IPrinterData[] {
        return categories.map((item) => {
            const savedItem = savedData.find((el) => el.categoryId === `${item.id}`);

            return {
                categoryId: item.id.toString(),
                category: item.name,
                printer: savedItem ? savedItem.printer : undefined,
            };
        });
    }
}
