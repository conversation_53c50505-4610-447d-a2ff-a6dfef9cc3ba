import { FC } from "react";
import ImageComponent from "../../../common/components/ImageComponent";
import { ProductSizeTypeEnum } from "../../../common/enums/DataEnums";
import PriceComponent from "../../../common/components/PriceComponent";

interface IProps {
    name: string;
    type?: "product" | "category" | "delivery_app";
    price?: number;
    onClick?: () => void;
    image?: string;
    className?: string;
    productSizeType?: ProductSizeTypeEnum;
}

const PosItemCardComponent: FC<IProps> = ({
    name,
    type = "product",
    price,
    onClick,
    image,
    className,
    productSizeType,
}) => {
    const isMultiple = productSizeType === ProductSizeTypeEnum.MULTIPLE;

    return (
        <div
            className={
                "border border-gray-400 bg-white p-2 rounded text-sm flex flex-col justify-between items-center gap-2 cursor-pointer" +
                " " +
                "hover:bg-gray-100 active:bg-gray-200 active:scale-95 h-44" +
                " " +
                className
            }
            onClick={onClick}
        >
            <div className="w-full">
                <ImageComponent
                    src={image}
                    alt="Product Image"
                    className="w-full m-auto h-28 rounded"
                    onErrorClassName="w-1/2 m-auto h-28"
                />
            </div>
            <div
                className={
                    "flex w-full font-tajawal-bold items-center" +
                    " " +
                    (type === "category" || type === "delivery_app" || isMultiple
                        ? "justify-center"
                        : "justify-between")
                }
            >
                <p>{name}</p>
                {!!price && !isMultiple && <PriceComponent price={price} />}
            </div>
        </div>
    );
};

export default PosItemCardComponent;
