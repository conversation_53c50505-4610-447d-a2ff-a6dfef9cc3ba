import { FC, useEffect, useState } from "react";
import { useTranslate } from "../hooks/useTranslate";
import { FaEye } from "react-icons/fa";
import { FaEyeSlash } from "react-icons/fa";

interface IProps {
    label?: string;
    className?: string;
    type?: React.HTMLInputTypeAttribute | "textarea" | "fixed";
    placeholder?: string;
    onChange?: (val: string) => void;
    value?: string;
    isError?: boolean;
    containerClassName?: string;
    isDebounce?: boolean;
    isDisabled?: boolean;
    textAreaRows?: number;
    textAreaResize?: boolean;
    rounded?: "rounded" | "rounded-full" | "rounded-none";
    border?: "border" | "border-none" | "border-b";
    showPasswordIcon?: boolean;
    icon?: React.ReactNode;
}

const InputComponent: FC<IProps> = ({
    label,
    className = "",
    type = "text",
    placeholder,
    onChange,
    value,
    isError,
    containerClassName = "",
    isDebounce,
    isDisabled = false,
    textAreaRows = 3,
    textAreaResize = false,
    rounded = "rounded",
    border = "border",
    showPasswordIcon = true,
    icon,
}) => {
    const { translate, isRtl } = useTranslate();
    const [showPassword, setShowPassword] = useState(false);
    let timer: ReturnType<typeof setTimeout> | undefined = undefined;

    const handleOnChange = (val: string) => {
        if (isDebounce) {
            if (timer) {
                clearTimeout(timer);
            }

            timer = setTimeout(() => {
                onChange && onChange(val);
            }, 500);
        } else {
            onChange && onChange(val);
        }
    };

    useEffect(() => {
        return () => {
            if (timer) {
                clearTimeout(timer);
            }
        };
    }, [timer]);

    return (
        <div className={"h-min flex flex-col" + " " + containerClassName}>
            {!!label && (
                <label className={`${isError ? "text-red-400" : ""}`}>
                    {translate(label)}
                </label>
            )}

            {type === "fixed" && (
                <div
                    className={
                        "flex items-center justify-between bg-base-100 font-tajawal-medium p-2 w-full border-gray-400 focus:outline-none text-black dark:text-white" +
                        " " +
                        rounded +
                        " " +
                        border +
                        " " +
                        (isRtl ? "text-right" : "text-left")
                    }
                >
                    <span>{translate(value || "")}</span>
                </div>
            )}

            {type === "textarea" && (
                <textarea
                    className={
                        `bg-base-100 font-tajawal-medium p-2 w-full focus:outline-none text-black dark:text-white` +
                        ` ${isError ? "border-red-400" : "border-gray-400"} ` +
                        ` ${textAreaResize ? "resize" : "resize-none"} ` +
                        ` ${rounded} ` +
                        ` ${border} ` +
                        " " +
                        (isRtl ? "text-right" : "text-left") +
                        " " +
                        className
                    }
                    placeholder={translate(placeholder || "")}
                    onChange={(e) => handleOnChange(e.target.value)}
                    value={value}
                    rows={textAreaRows}
                    disabled={isDisabled}
                />
            )}
            {type !== "textarea" && type !== "fixed" && (
                <div className="relative">
                    <input
                        type={showPassword ? "text" : type}
                        className={
                            `bg-base-100 font-tajawal-medium p-2 w-full focus:outline-none text-black dark:text-white  h-[2.6rem]` +
                            ` ${isError ? "border-red-400" : "border-gray-400"} ` +
                            ` ${rounded} ` +
                            ` ${border} ` +
                            " " +
                            (isRtl ? "text-right" : "text-left") +
                            " " +
                            className
                        }
                        placeholder={translate(placeholder || "")}
                        onChange={(e) => handleOnChange(e.target.value)}
                        value={value}
                        disabled={isDisabled}
                    />
                    {icon && (
                        <div className="absolute left-4 top-1/2 -translate-y-1/2">
                            {icon}
                        </div>
                    )}
                    {type === "password" && showPasswordIcon && (
                        <button
                            className={"absolute top-1/2 -translate-y-1/2" +
                                " " +
                                (isRtl ? "left-4" : "right-4")
                            }
                            onClick={(e) => {
                                e.preventDefault();
                                setShowPassword(!showPassword);
                            }}
                            type="button"
                        >
                            {showPassword ? <FaEyeSlash /> : <FaEye />}
                        </button>
                    )}
                </div>
            )}
        </div>
    );
};

export default InputComponent;
