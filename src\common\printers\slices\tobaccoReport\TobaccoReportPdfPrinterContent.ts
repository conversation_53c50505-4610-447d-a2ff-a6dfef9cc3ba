import { Content } from "pdfmake/interfaces";
import { ITobaccoReportPdfPrinterModel } from "./TobaccoReportPdfPrinterModel";
import { DateUtils } from "../../../utils/DateUtils";
import { RsSvg } from "../../../assets/svgs/RsSvg";
import { PdfMakeHelper } from "../../PdfMakeHelper";

export const TobaccoReportPdfPrinterContent = (model: ITobaccoReportPdfPrinterModel): Content[] => {
    return [
        {
            text: "تقرير ضريبة التبغ", style: { fontSize: 14, bold: true },
        },
        {
            text: DateUtils.format(model.endDate, "dd-MM-yyyy") + " - " + DateUtils.format(model.startDate, "dd-MM-yyyy"),
            margin: [0, 10, 0, 10],
        },
        {
            style: "table",
            table: {
                headerRows: 1,
                widths: ["auto", "auto", "auto", "auto", "auto", "auto", "auto", "*", "auto"],
                body: [
                    [
                        { text: "الإجمالي", style: "tableHeader" },
                        { text: "الضريبة", style: "tableHeader" },
                        { text: "ضريبة التبغ", style: "tableHeader" },
                        { text: "الإجمالي الفرعي", style: "tableHeader" },
                        { text: "الخصم", style: "tableHeader" },
                        { text: "الكمية", style: "tableHeader" },
                        { text: "السعر ", style: "tableHeader" },
                        { text: "الإسم", style: "tableHeader" },
                        { text: "الفاتورة", style: "tableHeader" },
                    ],
                    ...(model.items || []).map((el) => [
                        el.total.toFixed(2),
                        el.vat.toFixed(2),
                        el.tobaccoTax.toFixed(2),
                        el.subTotal.toFixed(2),
                        el.discount.toFixed(2),
                        el.quantity,
                        el.price.toFixed(2),
                        PdfMakeHelper.reverseTextIfArabic(el.name),
                        el.invoiceNumber,
                    ]),
                ],
            },
        },
        {
            margin: [0, 20, 10, 20],
            alignment: "right",
            bold: true,
            fontSize: 12,
            color: "#1F618D",
            stack: [
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.subTotal.toFixed(2), margin: [0, 4, 0, 0] },
                                        { text: "الإجمالي الفرعي:", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14, },
                                        { text: model.totals.discount.toFixed(2), margin: [0, 4, 0, 0] },
                                        { text: "الخصم: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.tobaccoTax.toFixed(2), margin: [0, 4, 0, 0] },
                                        { text: "ضريبة التبغ:", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    marginBottom: 5,
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.vat.toFixed(2), margin: [0, 4, 0, 0] },
                                        { text: "الضريبة: ", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
                {
                    columns: [
                        {
                            layout: "noBorders",
                            alignment: "right",
                            table: {
                                widths: ["*", "auto", "auto", "auto"],
                                body: [
                                    [
                                        { text: "" },
                                        { svg: RsSvg("#1F618D"), width: 14 },
                                        { text: model.totals.total.toFixed(2), margin: [0, 4, 0, 0] },
                                        { text: "الإجمالي:", margin: [0, 2, 0, 0] },
                                    ]
                                ]
                            }
                        }
                    ]
                },
            ],
        }
    ];
}