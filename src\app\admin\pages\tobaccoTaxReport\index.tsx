import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminTobaccoTaxReportTableHeaders } from "./AdminTobaccoTaxReportsConstants";
import GuardedRouteComponent from "../../../../common/components/GuardedRouteComponent";
import { RoutsConstants } from "../../../../common/constants/RoutesConstants";
import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsConstants } from "../../../../common/constants/ApiConstants";
import { OrderProductsApiRepo } from "../../../../common/repos/api/OrderProductsApi";
import { IOrderProductSumModel } from "../../../../common/models/OrderProductSumModel";
import { fixedNumber } from "../../../../common/utils/numberUtils";
import AdminTobaccoTaxReportTotalInfoFeature from "./features/AdminTobaccoTaxReportTotalInfoFeature";
import { useState } from "react";
import { AdminTobaccoTaxReportService } from "./TobaccoTaxReportService";

const AdminTobaccoTaxReportPage = () => {
    const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();
    const { isXs, isSm } = useScreenSize();
    const [dates, setDates] = useState({
        startDate: new Date(),
        endDate: new Date(),
    });

    const {
        data: ordersProductsSum,
        isLoading: ordersProductsSumLoading,
        isError: ordersProductsSumError,
        refetch: refetchOrderProductsSum,
    } = useFetch(
        "report-" + EndPointsConstants.ORDER_PRODUCTS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => OrderProductsApiRepo.sum(startTime, endTime, true)
    );

    const {
        data: ordersProducts,
        isLoading: ordersProductsLoading,
        isError: ordersProductsError,
        refetch: refetchOrderProducts,
    } = useFetch(
        "report-" + EndPointsConstants.ORDER_PRODUCTS_GET,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => OrderProductsApiRepo.getOrderProducts(startTime, endTime, true)
    );

    const onDate = (startDate: Date, endDate: Date) => {
        const start = DateUtils.getStartOfDayNumber(startDate);
        const end = DateUtils.getEndOfDayNumber(endDate);
        setDates({ startDate, endDate });
        refetchOrderProducts(start, end, true);
        refetchOrderProductsSum(start, end, true);
    };

    const onDownload = () => 
        AdminTobaccoTaxReportService.handleDownload(dates, ordersProducts, ordersProductsSum);

    return (
        <GuardedRouteComponent
            guard={hasTobaccoTax}
            pathToRedirect={RoutsConstants.admin.categories.fullPath}
        >
            <div className="flex flex-col gap-2">
                <AdminReportControllerComponent
                    onDate={onDate}
                    onDownload={onDownload}
                    isDownloadDisabled={!ordersProducts?.length || !ordersProductsSum}
                />
                <AdminTobaccoTaxReportTotalInfoFeature orderProductSum={ordersProductsSum} />
                <StatusComponent
                    isEmpty={!ordersProducts?.length}
                    isLoading={ordersProductsLoading || ordersProductsSumLoading}
                    isError={ordersProductsError || ordersProductsSumError}
                    height={isXs ? 14.6 : isSm ? 11.6 : 12.6}
                >
                    <TableComponent
                        headers={AdminTobaccoTaxReportTableHeaders}
                        items={ordersProducts || []}
                        selectors={(item: IOrderProductSumModel) => [
                            item.invoiceNumber,
                            item.name,
                            fixedNumber(item.price),
                            item.quantity,
                            fixedNumber(item.discount) || "-",
                            fixedNumber(item.subTotal),
                            fixedNumber(item.tobaccoTax),
                            fixedNumber(item.vat),
                            fixedNumber(item.total),
                            DateUtils.format(item.startTime),
                        ]}
                    />
                </StatusComponent>
            </div>
        </GuardedRouteComponent>
    );
};

export default AdminTobaccoTaxReportPage;
