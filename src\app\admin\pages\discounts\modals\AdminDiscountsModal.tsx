import { FC } from "react";
import { IAdminDiscountInputs } from "../AdminDiscountsInterface";
import { AdminDiscountInputs } from "../AdminDiscountsConstants";
import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import { DiscountsApiRepo } from "../../../../../common/repos/api/DiscountsApiRepo";
import { EndPointsConstants } from "../../../../../common/constants/ApiConstants";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { AdminDiscountsValidation } from "../AdminDiscountsValidation";
import useCustomState from "../../../../../common/hooks/useCustomState";
import { IDiscountModel } from "../../../../../common/models/DiscountModel";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import ToggleButtonComponent from "../../../../../common/components/ToggleButtonComponent";

interface IProps {
    item?: IDiscountModel;
}

const AdminDiscountsModal: FC<IProps> = ({ item }) => {
    const [state, setState, resetState] = useCustomState<IAdminDiscountInputs>(
        AdminDiscountInputs,
        item
    );
    const addDiscount = useFlatMutate(DiscountsApiRepo.addDiscount, {
        updateCached: { key: EndPointsConstants.DISCOUNTS, operation: "add" },
        afterEnd: () => resetState(),
    });
    const updateDiscount = useFlatMutate(DiscountsApiRepo.updateDiscount, {
        updateCached: {
            key: EndPointsConstants.DISCOUNTS,
            operation: "update",
            selector: (data: IDiscountModel) => data.id,
        },
        closeModalOnSuccess: true,
    });

    const onClick = () => {
        const isValid = AdminDiscountsValidation.inputsValidation(state);
        if (!isValid) return;
        if (item) return updateDiscount(item.id, state);
        addDiscount(state);
    };

    const handleAmountChange = (amount: string) => {
        if (!item) {
            let name = TranslateHelper.t(TranslateConstants.DISCOUNT) + ` ${amount}%`;
            setState({ ...state, amount, name: amount ? name : "" });
            return;
        }

        setState({ ...state, amount });
    }

    return (
        <>
            <form className="px-2">
                <InputComponent
                    label={TranslateConstants.THE_DISCOUNT}
                    onChange={handleAmountChange}
                    value={state.amount}
                    placeholder="0.00"
                />
                <InputComponent
                    label={TranslateConstants.NAME}
                    onChange={(name) => setState({ ...state, name })}
                    value={state.name}
                />
                <ToggleButtonComponent
                    onChange={(active) => setState({ ...state, active })}
                    label={TranslateConstants.ACTIVATION}
                    value={state.active}
                    className="mt-2"
                />
            </form>
            <ModalButtonsComponent text={TranslateConstants.SAVE} onClick={onClick} />
        </>
    );
};

export default AdminDiscountsModal;
