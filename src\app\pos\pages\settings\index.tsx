import ListComponent from "../../../../common/components/ListComponent";
import PosQuickSettingsFeature from "./features/PosQuickSettingsFeature";
import PosPrinterSettingsFeature from "./features/PosPrinterSettingsFeature";
import PosSettingsMenuFeature from "./features/PosSettingsMenuFeature";
import { useEffect, useState } from "react";
import { IPrinterModel } from "../../../../common/models/PrinterModel";
import useActions from "../../../../common/redux/data/useActions";
import { PosSettingsService } from "./PosSettingsService";

const PosSettingsPage = () => {
    const [printers, setPrinters] = useState<IPrinterModel[]>([]);
    const actions = useActions();

    useEffect(() => {
        PosSettingsService.init(actions, setPrinters);
    }, []);

    return (
        <div className="flex h-s p-4 gap-4 text-slate-500">
            <div className="col-span-7 flex-1">
                <ListComponent calcHeight={9} padding="0 pb-2" allowScrollBar={false}>
                    <PosPrinterSettingsFeature printers={printers} />
                </ListComponent>
                <div className="flex gap-4">
                    <PosSettingsMenuFeature />
                </div>
            </div>
            <div className="flex flex-col w-2/6 bg-white rounded-2xl shadow p-2 text-center gap-2">
                <PosQuickSettingsFeature />
            </div>
        </div>
    );
};

export default PosSettingsPage;
