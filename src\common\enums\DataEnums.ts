export enum OrderTypeEnum {
    DINE_IN = 'DINE_IN',
    TAKE_AWAY = 'TAKE_AWAY',
    DELIVERY = 'DELIVERY',
    DELIVERY_APP = 'DELIVERY_APP',
}

export enum OrderStatusEnum {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN_PROGRESS',
    COMPLETED = 'COMPLETED',
    CANCELLED = 'CANCELLED',
}

export enum PrinterEnum {
    RECEIPT = 'RECEIPT',
}

export enum paymentsEnum {
    CASH = 'CASH',
    NETWORK = 'NETWORK',
}

export enum TableStatusEnum {
    OCCUPIED = 'OCCUPIED',
    AVAILABLE = 'AVAILABLE',
}

export enum KitchenOrderStatusEnum {
    NEW = 'NEW',
    EDIT = 'EDIT',
    DELETE = 'DELETE',
}

export enum ProductSizeTypeEnum {
    FIXED = 'fixed',
    MULTIPLE = 'multiple',
}

export enum OrganizationRoleEnum {
    ADMIN = 'ADMIN',
    POS = 'POS',
}

export enum DeliveryAppEnum {
    HUNGER_STATION = 'HUNGER_STATION',
    JAEHEZ = 'JAEHEZ',
    TO_YOU = 'TO_YOU',
    THE_CHEFZ = 'THE_CHEFZ',
    KEETA = 'KEETA',
    NINJA = 'NINJA',
    MESHWAR_FOOD = 'MESHWAR_FOOD',
    LOCATE = 'LOCATE',
}