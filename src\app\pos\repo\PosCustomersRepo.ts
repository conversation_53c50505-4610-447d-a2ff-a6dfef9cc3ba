import {
    APP_LOCAL_DB_COLLECTIONS,
    AppLocalDB,
} from "../../../common/config/localDB";
import { EndPointsConstants } from "../../../common/constants/ApiConstants";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { ICustomerModel } from "../../../common/models/CustomerModel";
import { debug } from "../../../common/utils/CommonUtils";

export class PosCustomersRepo {
    static async getAndCacheCustomers(updatedAt?: Date) {
        try {
            const res = await AxiosHelper.get<ICustomerModel[]>(
                EndPointsConstants.CUSTOMERS,
                { params: { updatedAt } }
            );

            if (!res.success) throw new Error(res.message);
            const data = res.data || [];

            if (!updatedAt)
                await AppLocalDB.set(APP_LOCAL_DB_COLLECTIONS.CUSTOMERS, data);
            else await AppLocalDB.add(APP_LOCAL_DB_COLLECTIONS.CUSTOMERS, data, true);
        } catch (error) {
            debug(`PosCustomersRepo [getAndCacheCustomers] Error: `, error);
            throw error;
        }
    }

    static async getCustomers(activeOnly: boolean = false) {
        try {
            const res = await AppLocalDB.get<ICustomerModel>(
                APP_LOCAL_DB_COLLECTIONS.CUSTOMERS,
                {
                    where: activeOnly ? [[["active", "==", true]]] : undefined,
                    orderBy: ["id", "desc"],
                }
            );

            return res;
        } catch (error) {
            debug(`PosCustomersRepo [getCustomers] Error: `, error);
            throw error;
        }
    }

    static async searchCustomer(
        mobile: string
    ): Promise<ICustomerModel | undefined> {
        try {
            let res = await AppLocalDB.getOne<ICustomerModel>(
                APP_LOCAL_DB_COLLECTIONS.CUSTOMERS,
                ["mobile", "==", mobile]
            );

            return res;
        } catch (error) {
            debug(`PosCustomersRepo [searchCustomer] Error: `, error);
            throw error;
        }
    }

    static async addCustomer(customer: ICustomerModel) {
        try {
            const res = await AppLocalDB.add(
                APP_LOCAL_DB_COLLECTIONS.CUSTOMERS,
                customer,
                true
            );
            return res;
        } catch (error) {
            debug(`PosCustomersRepo [addCustomer] Error: `, error);
            throw error;
        }
    }

    static async updateCustomer(id: number, customer: ICustomerModel) {
        try {
            const res = await AppLocalDB.update(
                APP_LOCAL_DB_COLLECTIONS.CUSTOMERS,
                id,
                customer
            );
            return res;
        } catch (error) {
            debug(`PosCustomersRepo [updateCustomer] Error: `, error);
            throw error;
        }
    }
}
