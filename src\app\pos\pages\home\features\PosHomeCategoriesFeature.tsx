import { FC } from "react";
import PosItemCardComponent from "../../../components/PosItemCardComponent";
import { ICategoryModel } from "../../../../../common/models/CategoryModel";
import usePosActions from "../../../redux/usePosActions";
import { PosHomeService } from "../PosHomeService";

interface IProps {
    categories: ICategoryModel[];
}

const PosHomeCategoriesFeature: FC<IProps> = ({ categories }) => {
    const posActions = usePosActions();

    const handleOnClick = (selectedCategory: ICategoryModel) =>
        PosHomeService.handelSelectCategory(selectedCategory, posActions);

    return (
        <>
            {categories.map((category) => (
                <PosItemCardComponent
                    key={category.id}
                    name={category.name}
                    type="category"
                    image={category.image}
                    onClick={() => handleOnClick(category)}
                />
            ))}
        </>
    );
};

export default PosHomeCategoriesFeature;
