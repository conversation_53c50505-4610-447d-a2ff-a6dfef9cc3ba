import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import { IShiftModel } from "../../../../common/models/ShiftModel";
import { IShiftSumModel } from "../../../../common/models/ShiftsSumModel";
import { PdfMakeUtils } from "../../../../common/printers/PdfMakeUtils";
import { ShiftReportPdfPrinterContent } from "../../../../common/printers/slices/shiftReport/ShiftReportPdfPrinterContent";
import { IShiftReportPdfPrinterModel } from "../../../../common/printers/slices/shiftReport/ShiftReportPdfPrinterModel";

export class AdminShiftReportService {
    static async handleDownload(
        dateRange: { startTime: number; endTime: number },
        shifts: IShiftModel[] | undefined,
        shiftsSum: IShiftSumModel | undefined
    ) {
        const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();

        const model: IShiftReportPdfPrinterModel = {
            startDate: new Date(dateRange.startTime),
            endDate: new Date(dateRange.endTime),
            items: shifts?.map((el) => ({
                ordersCount: el.ordersCount,
                startAmount: el.startAmount,
                endAmount: el.endAmount,
                discountAmount: el.discountAmount,
                tobaccoTaxAmount: hasTobaccoTax ? el.tobaccoTaxAmount : undefined,
                vatAmount: el.vatAmount,
                totalAmount: el.totalAmount,
                cashAmount: el.cashAmount,
                networkAmount: el.networkAmount,
                deliveryAppsAmount: el.deliveryAppsAmount ?? 0,
                additionAmount: el.additionAmount?? 0,
                shortageAmount: el.shortageAmount ?? 0,
            })),
            totals: {
                ordersCount: shiftsSum?.ordersCount ?? 0,
                totalAmount: shiftsSum?.totalAmount ?? 0,
                vatAmount: shiftsSum?.vatAmount ?? 0,
                additionAmount: shiftsSum?.additionAmount ?? 0,
                shortageAmount: shiftsSum?.shortageAmount ?? 0,
            },
        };
        PdfMakeUtils.preview(ShiftReportPdfPrinterContent(model), { isLandScape: true });
    }
}