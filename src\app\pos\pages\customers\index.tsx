import useFetch from "../../../../common/asyncController/useFetch";
import StatusComponent from "../../../../common/components/StatusComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import { useTranslate } from "../../../../common/hooks/useTranslate";
import { ICustomerModel } from "../../../../common/models/CustomerModel";
import useActions from "../../../../common/redux/data/useActions";
import PosBackButtonComponent from "../../components/PosBackButtonComponent";
import { PosCustomersRepo } from "../../repo/PosCustomersRepo";
import PosCustomersModal from "./modals/PosCustomersModal";
import { PosCustomersTableHeaderConstants } from "./PosCustomersConstants";

const PosCustomersPage = () => {
    const { translate } = useTranslate();
    const actions = useActions();

    const { data, isLoading, isError } = useFetch(
        "pos-customers",
        PosCustomersRepo.getCustomers,
        { autoFetchOnMount: true }
    );

    const handleOnEdit = (item: ICustomerModel) => {
        if (!item.active) {
            ToastHelper.error(translate(TranslateConstants.CANT_EDIT_INACTIVE_CUSTOMER));
            return;
        }

        actions.openModal({
            size: "lg",
            title: TranslateHelper.t(TranslateConstants.EDIT_CUSTOMER),
            showButtons: false,
            component: <PosCustomersModal item={item} />,
        });
    };

    return (
        <div className="p-4 flex flex-col gap-2">
            <div className="font-tajawal-bold text-2xl">
                {translate(TranslateConstants.CUSTOMERS)}
            </div>
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={12}
            >
                <TableComponent
                    headers={PosCustomersTableHeaderConstants}
                    items={data || []}
                    selectors={(item: ICustomerModel) => {
                        const statusColor = () => {
                            if (item.active) return "text-green-600";
                            return "text-red-600";
                        };

                        return [
                            item.number,
                            item.name,
                            item.mobile,
                            item.taxNumber,
                            item.address,
                            <div className={statusColor()}>
                                {translate(
                                    item.active
                                        ? TranslateConstants.ACTIVE
                                        : TranslateConstants.INACTIVE
                                )}
                            </div>,
                        ];
                    }}
                    showEditButton={true}
                    onEdit={handleOnEdit}
                />
            </StatusComponent>
            <PosBackButtonComponent />
        </div>
    );
};

export default PosCustomersPage;
