import { EndPointsConstants } from "../../constants/ApiConstants";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IDeliveryAppProductBody } from "../../interfaces/body/DeliveryAppProductBody";
import { debug } from "../../utils/CommonUtils";

export class DeliveryAppsProductsApiRepo {
    static async setDeliveryAppProducts(
        products: IDeliveryAppProductBody[],
    ) {
        try {
            const res = await AxiosHelper.post<any>(EndPointsConstants.PRODUCTS_DELIVERY_APPS, products);
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`DeliveryAppsProductsApiRepo [setDeliveryAppProducts] Error: `, error);
            throw error;
        }
    }

    static async setDeliveryAppSizes(
        products: IDeliveryAppProductBody[],
    ) {
        try {
            const res = await AxiosHelper.post<any>(EndPointsConstants.SIZES_DELIVERY_APPS, products);
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`DeliveryAppsProductsApiRepo [setDeliveryAppSizes] Error: `, error);
            throw error;
        }
    }

    static async setDeliveryAppAdditions(
        products: IDeliveryAppProductBody[],
    ) {
        try {
            const res = await AxiosHelper.post<any>(EndPointsConstants.ADDITIONS_DELIVERY_APPS, products);
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`DeliveryAppsProductsApiRepo [setDeliveryAppAdditions] Error: `, error);
            throw error;
        }
    }
}
