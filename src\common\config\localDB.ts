import { LocalDB } from "../LocalDB/LocalDB";
import { ILocalDBStore } from "../LocalDB/LocalDBInterface";

export const APP_LOCAL_DB_COLLECTIONS = {
    CATEGORIES: "categories",
    PRODUCTS: "products",
    TABLES: "tables",
    ORDERS: "orders",
    SHIFT: "shift",
    DISCOUNTS: "discounts",
    CUSTOMERS: "customers",
    ADDITIONS: "additions",
} as const;

export type APP_LOCAL_DB_COLLECTIONS_TYPE = (typeof APP_LOCAL_DB_COLLECTIONS)[keyof typeof APP_LOCAL_DB_COLLECTIONS];

const stores: ILocalDBStore[] = [
    {
        collection: APP_LOCAL_DB_COLLECTIONS.CATEGORIES,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.PRODUCTS,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.TABLES,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.ORDERS,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.SHIFT,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.DISCOUNTS,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.CUSTOMERS,
        indexes: [],
    },
    {
        collection: APP_LOCAL_DB_COLLECTIONS.ADDITIONS,
        indexes: [],
    },
] as const;

export type APP_LOCAL_DB_INDEXES_TYPE = (typeof stores)[number]["indexes"][number]["name"];

export const AppLocalDB = new LocalDB("AppLocalDB", stores, 5);