import { IUpdatesMetaData } from "../../app/pos/interface";
import {
    lastPosUpdateAtKeyConstant,
    organizationKeyConstant,
    updatesCountKeyConstant,
} from "../constants/ConfigConstants";
import { OrganizationRoleEnum } from "../enums/DataEnums";
import { OrganizationModel } from "../models/OrganizationModel";
import { IOrganizationUpdatesCountModel } from "../models/OrganizationUpdatesCountModel";
import { DateUtils } from "../utils/DateUtils";
import { LocalStorageHelper } from "./LocalStorageHelper";

export class OrganizationHelper {
    static setOrganization(organization: OrganizationModel) {
        LocalStorageHelper.set(organizationKeyConstant, organization);
    }

    static getOrganization(): OrganizationModel | undefined {
        return LocalStorageHelper.get(organizationKeyConstant);
    }

    static hasVat(): boolean {
        const organization = this.getOrganization();
        return !!organization?.taxNumber;
    }

    static hasTobaccoTax(): boolean {
        const organization = this.getOrganization();
        return !!organization?.activateTobaccoTax;
    }

    static removeOrganization() {
        LocalStorageHelper.remove(organizationKeyConstant);
    }

    static getUpdateMetaData(): IUpdatesMetaData {
        const updatesCount: IOrganizationUpdatesCountModel = LocalStorageHelper.get(
            updatesCountKeyConstant
        );
        const lastPosUpdateAt: Date = LocalStorageHelper.get(
            lastPosUpdateAtKeyConstant
        );
        return { ...updatesCount, lastPosUpdateAt };
    }

    static setUpdatesMetaData(updates: IOrganizationUpdatesCountModel) {
        LocalStorageHelper.set(updatesCountKeyConstant, updates);
        LocalStorageHelper.set(
            lastPosUpdateAtKeyConstant,
            DateUtils.timeZone()
        );
    }

    static needToUpdate(updates: IOrganizationUpdatesCountModel): boolean {
        const updatesCount = LocalStorageHelper.get(updatesCountKeyConstant);
        return JSON.stringify(updates) !== JSON.stringify(updatesCount);
    }

    static setRole(role: OrganizationRoleEnum) {
        const organization = this.getOrganization();
        if (organization) {
            organization.role = role;
            this.setOrganization(organization);
        }
    }

    static removeRole() {
        const organization = this.getOrganization();
        if (organization) {
            organization.role = undefined;
            this.setOrganization(organization);
        }
    }

    static hasRole(): boolean {
        const organization = this.getOrganization();
        return organization?.role !== undefined;
    }

    static isAdmin(): boolean {
        const organization = this.getOrganization();
        return organization?.role === OrganizationRoleEnum.ADMIN;
    }

    static isPos(): boolean {
        const organization = this.getOrganization();
        return organization?.role === OrganizationRoleEnum.POS;
    }
}