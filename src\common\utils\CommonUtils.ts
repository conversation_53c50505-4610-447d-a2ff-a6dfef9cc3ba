import { IPosOrder } from "../../app/pos/interface";
import { TranslateConstants } from "../constants/TranslateConstants";
import { OrderTypeEnum } from "../enums/DataEnums";
import { AppHelper } from "../helpers/AppHelper";
import { TranslateHelper } from "../helpers/TranslateHelper";

export const debug = (...val: any) => {
    if (AppHelper.isDevMode()) console.error(val[0], ...val.slice(1));
};

export const delay = (time: number, val?: any) => {
    return new Promise((res) => {
        setTimeout(() => {
            res(val);
        }, time);
    });
};

export const isOnlineState = navigator.onLine;

export const getOrderType = (order: IPosOrder) => {
    if (order.type === OrderTypeEnum.DELIVERY_APP && order.deliveryApp) {
        return `${TranslateHelper.t(TranslateConstants.APP)} ${TranslateHelper.t(order.deliveryApp)}`;
    }

    return TranslateHelper.t(order.type);
};

export const isArabic = (text: string) => /[\u0600-\u06FF]/.test(text);


export async function imageUrlToBase64(url: string): Promise<string> {
    const response = await fetch(url, { mode: "cors" });
    const blob = await response.blob();
    return await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
    });
}
