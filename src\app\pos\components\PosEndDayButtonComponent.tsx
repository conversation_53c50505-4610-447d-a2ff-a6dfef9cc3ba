import ButtonComponent from "../../../common/components/ButtonComponent";
import { TranslateConstants } from "../../../common/constants/TranslateConstants";
import { BsCloudArrowUp } from "react-icons/bs";
import { useAppSelector } from "../../../common/redux/store";
import { posHomeSelector } from "../redux/selector";
import { useEffect, useState } from "react";
import { PosOrderRepo } from "../repo/PosOrderRepo";
import { PosShiftRepo } from "../repo/PosShiftRepo";
import { debug } from "../../../common/utils/CommonUtils";
import { ToastHelper } from "../../../common/helpers/ToastHelper";
import useOnlineStatus from "../../../common/hooks/useOnlineStatus";
import useActions from "../../../common/redux/data/useActions";
import { IoWarningOutline } from "react-icons/io5";
import { PosShiftService } from "../services/PosShiftService";

const PosEndDayButtonComponent = () => {
    const { isShiftOpen } = useAppSelector(posHomeSelector);
    const isOnline = useOnlineStatus();
    const [isDisabled, setIsDisabled] = useState(false);
    const actions = useActions();

    const checkIsDisabled = async () => {
        const ordersCount = await PosOrderRepo.ordersCount();
        const shiftsCount = await PosShiftRepo.shiftsCount();

        setIsDisabled(!isOnline || !!isShiftOpen || (!ordersCount && !shiftsCount));
    };

    useEffect(() => {
        checkIsDisabled();
    }, [isShiftOpen, isOnline]);

    const handleOnClick = async () => {
        try {
            actions.setLoading();
            await PosShiftService.endDay();
            setIsDisabled(true);
            ToastHelper.success(TranslateConstants.END_DAY_SUCCESSFULLY);
        } catch (error) {
            ToastHelper.error(TranslateConstants.ERROR_END_DAY);
            debug(`PosEndDayButtonComponent [handleOnClick] Error: `, error);
        } finally {
            actions.setLoading(false);
        }
    };

    return (
        <ButtonComponent
            text={TranslateConstants.END_THE_DAY}
            iconComponent={
                isOnline ? (
                    <BsCloudArrowUp className="text-xl" />
                ) : (
                    <IoWarningOutline className="text-xl text-red-500" />
                )
            }
            bgColor="base-200"
            isCentered={false}
            className="flex-row-reverse py-3 rounded-lg"
            textColor="black"
            isBorder={true}
            onClick={handleOnClick}
            isDisabled={isDisabled}
        />
    );
};

export default PosEndDayButtonComponent;
