import { FC, useState } from "react";
import {
    IProductModel,
    IProductSizeModel,
} from "../../../../../common/models/ProductModel";
import ListComponent from "../../../../../common/components/ListComponent";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { PosHomeService } from "../PosHomeService";
import usePosActions from "../../../redux/usePosActions";
import useActions from "../../../../../common/redux/data/useActions";
import PosOrderAdditionsModal from "../../../containers/order/modals/PosOrderAdditionsModal";
import PriceComponent from "../../../../../common/components/PriceComponent";
import { IPosOrder } from "../../../interface";
import { OrderTypeEnum } from "../../../../../common/enums/DataEnums";
import { DeliveryAppsHelper } from "../../../../../common/helpers/DeliveryAppsHelper";
import { PriceUtils } from "../../../../../common/utils/PriceUtils";

interface IProps {
    product: IProductModel;
    order: IPosOrder;
}

const PosHomeProductSizesModal: FC<IProps> = ({ product, order }) => {
    const actions = useActions();
    const posActions = usePosActions();
    const [showAdditions, setShowAdditions] = useState(false)

    const filteredSizes = product.sizes.filter((size) => {
        if (order.type === OrderTypeEnum.DELIVERY_APP && order.deliveryApp) {
            const { isActiveKey } = DeliveryAppsHelper.getDeliverAppKeys(order.deliveryApp);
            return (size.deliveryApps as any)?.[isActiveKey];
        }

        return true;
    });

    const handleOnClick = (size: IProductSizeModel) => {
        if (product.isIncludingAdditions) {
            setShowAdditions(true)
            actions.openModal({
                title: TranslateHelper.t(TranslateConstants.ADDITIONS) + " (" + product.name + ")",
            });
        }
        PosHomeService.handleAddProductToOrder(product, posActions, order, size);
    }

    const handleOnBack = () => {
        setShowAdditions(false)
        actions.openModal({
            title: TranslateHelper.t(TranslateConstants.SIZES) + " (" + product.name + ")",
        });
    }

    if (showAdditions) return <PosOrderAdditionsModal onBack={handleOnBack} />
    return (
        <ListComponent padding="0 px-2" cols={2} calcHeight={30}>
            {filteredSizes.map((el, index) => {
                return (
                    <div
                        key={index}
                        className={
                            "flex flex-col border border-gray-400 text-lg rounded p-2 py-5 items-center font-tajawal-bold gap-3" +
                            " " +
                            "cursor-pointer hover:scale-95 active:bg-base-200 shadow-md"
                        }
                        onClick={() => handleOnClick(el)}
                    >
                        <span>{el.name}</span>
                        <PriceComponent
                            price={PriceUtils.getProductPrice(el, order)}
                            iconWidth="w-4"
                        />
                    </div>
                );
            })}
        </ListComponent>
    );
};

export default PosHomeProductSizesModal;
