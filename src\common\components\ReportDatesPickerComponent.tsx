import { FC, useState } from "react";
import { TranslateConstants } from "../constants/TranslateConstants";
import DatePickerComponent from "./DatePickerComponent";
import { DateUtils } from "../utils/DateUtils";
import useScreenSize from "../hooks/useScreenSize";
import { ToastHelper } from "../helpers/ToastHelper";
import { useTranslate } from "../hooks/useTranslate";
import IconButtonComponent from "./IconButtonComponent";
import { FiSearch } from "react-icons/fi";

interface IProps {
    onClick?: (startDate: Date, endDate: Date) => void;
    disableButton?: boolean;
}

const ReportDatesPickerComponent: FC<IProps> = ({ onClick, disableButton }) => {
    const { translate } = useTranslate();
    const { isSm } = useScreenSize();
    const [state, setState] = useState({
        startDate: DateUtils.getStartOfDay(),
        endDate: DateUtils.getEndOfDay(),
        disabled: true,
    });

    const handleOnCLik = () => {
        if (state.startDate > state.endDate) {
            ToastHelper.error(TranslateConstants.START_DATE_GREATER_THAN_END_DATE);
            return;
        }
        onClick?.(state.startDate, state.endDate);
        setState({ ...state, disabled: true });
    };

    return (
        <div className="flex gap-2 w-full sm:w-min">
            <DatePickerComponent
                onChange={(startDate) => setState({ ...state, startDate, disabled: false })}
                label={translate(
                    isSm ? TranslateConstants.FROM : TranslateConstants.FROM_DATE
                )}
                defaultValue={{
                    startDate: state.endDate,
                    endDate: state.endDate,
                }}
            />
            <DatePickerComponent
                onChange={(_, endDate) => setState({ ...state, endDate, disabled: false })}
                label={translate(
                    isSm ? TranslateConstants.TO : TranslateConstants.TO_DATE
                )}
                defaultValue={{
                    startDate: state.startDate,
                    endDate: state.startDate,
                }}
                minDate={state.startDate}
            />
            <IconButtonComponent
                icon={<FiSearch />}
                onClick={handleOnCLik}
                className="!w-16 px-4 py-[0.55rem]"
                isDisabled={state.disabled && disableButton}
            />
        </div>
    );
};

export default ReportDatesPickerComponent;
