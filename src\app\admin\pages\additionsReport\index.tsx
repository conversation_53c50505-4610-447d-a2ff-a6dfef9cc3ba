import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminAdditionsReportTableHeaders } from "./AdminMostSellingProductsReportsConstants";
import { IMostSellingProductsModel } from "../../../../common/models/MostSellingProductsModel";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsConstants } from "../../../../common/constants/ApiConstants";
import { AdditionsApiRepo } from "../../../../common/repos/api/AdditionsApiRepo";

const AdminAdditionReportPage = () => {
    const { isXs, isSm } = useScreenSize();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsConstants.ORDER_PRODUCTS_ADDITIONS_SUM,
        (
            startTime = DateUtils.getStartOfDayNumber(),
            endTime = DateUtils.getEndOfDayNumber()
        ) => AdditionsApiRepo.getSum(startTime, endTime)
    );

    const onDate = (startDate: Date, endDate: Date) =>
        refetch(
            DateUtils.getStartOfDayNumber(startDate),
            DateUtils.getEndOfDayNumber(endDate)
        );

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent onDate={onDate} />
            <StatusComponent
                isEmpty={!data?.length}
                isLoading={isLoading}
                isError={isError}
                height={isXs ? 10.6 : isSm ? 8 : 8.1}
            >
                <TableComponent
                    headers={AdminAdditionsReportTableHeaders}
                    items={data || []}
                    selectors={(item: IMostSellingProductsModel) => [
                        item.name,
                        item.quantity,
                        item.discount.toFixed(2),
                        item.subTotal.toFixed(2),
                        item.vat.toFixed(2),
                        item.total.toFixed(2),
                    ]}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminAdditionReportPage;
