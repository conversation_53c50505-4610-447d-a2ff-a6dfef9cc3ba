import { FC, useEffect, useRef, useState } from "react";
import ListComponent from "./ListComponent";
import { TranslateConstants } from "../constants/TranslateConstants";
import ListTileItemComponent from "./ListTileItemComponent";
import SpineComponent from "./SpineComponent";
import { useTranslate } from "../hooks/useTranslate";
import { FaSearch } from "react-icons/fa";
import { MdErrorOutline } from "react-icons/md";

interface IProps {
    label?: string;
    isError?: boolean;
    placeholder?: string;
    items?: any[];
    titleSelector?: (item: any) => any;
    subtitleSelector?: (item: any) => any;
    onFilter?: (input: string, item: any) => any;
    onSelect?: (item: any) => any;
    defaultValue?: string;
    showCount?: boolean;
    isSearchable?: boolean;
    isNullable?: boolean;
    isOutFilter?: boolean;
    onChange?: (value: string) => void;
    isLoading?: boolean;
    containerClassName?: string;
    className?: string;
    showViewButton?: boolean;
    showSearchIcon?: boolean;
}

const DropDownSearchComponent: FC<IProps> = ({
    placeholder,
    items,
    titleSelector,
    subtitleSelector,
    onFilter,
    onSelect,
    defaultValue = "",
    label,
    isError,
    showCount = false,
    isSearchable = true,
    isNullable = false,
    isOutFilter = false,
    onChange,
    isLoading = false,
    containerClassName = "",
    className = "",
    showViewButton = true,
    showSearchIcon = false,
}) => {
    const { translate, isRtl } = useTranslate();
    const [open, setOpen] = useState(false);
    const [input, setInput] = useState("");
    const [filteredItems, setFilteredItems] = useState(items);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);
    const [selectedItem, setSelectedItem] = useState(undefined);

    const handleClickOutside = (event: any) => {
        if (
            open &&
            dropdownRef.current &&
            !dropdownRef.current.contains(event.target)
        ) {
            setOpen(false);
            if (inputRef.current && isOutFilter) {
                inputRef.current.value = selectedItem
                    ? titleSelector
                        ? titleSelector(selectedItem)
                        : `${selectedItem}`
                    : "";
            }
        }
    };

    useEffect(() => {
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [open]);

    useEffect(() => {
        setFilteredItems(items);
    }, [items]);

    useEffect(() => {
        if (!items?.length || !isSearchable || isOutFilter) return;
        if (!input) {
            setFilteredItems(items);
            return;
        }

        const filtered = items.filter((el) => {
            if (onFilter) {
                return onFilter(input, el);
            }

            const title = titleSelector ? titleSelector(el) : `${el}`;
            const subtitle = subtitleSelector ? subtitleSelector(el) : "";

            return (
                title.toLowerCase().includes(input.toLowerCase()) ||
                subtitle.toLowerCase().includes(input.toLowerCase())
            );
        });

        setFilteredItems(filtered);
    }, [input, items]);

    useEffect(() => {
        if (isOutFilter && inputRef.current && !inputRef.current.value) {
            inputRef.current.value = defaultValue;
            return;
        }
        if (!!defaultValue) setInput(defaultValue);
    }, [defaultValue]);

    const handleInputClick = () => {
        if (!items?.length || open) return;
        setOpen(true);
    };

    const handleButtonClick = (
        e: React.MouseEvent<HTMLButtonElement, MouseEvent>
    ) => {
        e.preventDefault();
        if (!items?.length || isLoading) return;

        if (isOutFilter && inputRef.current) {
            if (!open && selectedItem) {
                setSelectedItem(undefined);
                inputRef.current.value = "";
                onSelect && onSelect(undefined);
                return;
            }
            if (open && selectedItem) {
                inputRef.current.value = titleSelector
                    ? titleSelector(selectedItem)
                    : `${selectedItem}`;
            }
        } else {
            if (!open && !!input && isNullable) {
                setInput("");
                onSelect && onSelect(null);
                return;
            }
            isSearchable && setInput(defaultValue);
        }

        setOpen(!open);
    };

    let timer: ReturnType<typeof setTimeout> | undefined = undefined;

    useEffect(() => {
        return () => {
            if (timer) {
                clearTimeout(timer);
            }
        };
    }, [timer]);

    const handleInputChange = (e: string) => {
        if ((!isOutFilter && !items?.length) || !isSearchable) return;

        if (!isOutFilter) {
            setInput(e);
        }

        if (onChange && isOutFilter) {
            if (timer) {
                clearTimeout(timer);
            }

            timer = setTimeout(() => {
                onChange(e);
            }, 500);
        }
    };

    const handleOnItemClick = (item: any) => {
        if (onSelect) {
            onSelect(item);
        }

        if (isOutFilter && inputRef.current) {
            inputRef.current.value = titleSelector ? titleSelector(item) : `${item}`;
        } else {
            setInput(titleSelector ? titleSelector(item) : `${item}`);
        }

        setSelectedItem(item);
        setOpen(false);
    };

    return (
        <div className={"relative" + " " + containerClassName} ref={dropdownRef}>
            {!!label && (
                <label className={`${isError ? "text-red-400" : ""}`}>
                    {translate(label)}
                </label>
            )}
            <div
                className={
                    "flex border rounded border-gray-400" +
                    " " +
                    ` ${isError ? "border-red-400" : "border-gray-400"} ` +
                    " " +
                    className
                }
            >
                <div className="relative flex-1">
                    <input
                        type="text"
                        className={
                            `rounded bg-base-100 font-tajawal-medium p-2 w-full focus:outline-none text-black dark:text-white` +
                            " " +
                            `${!isSearchable ? "caret-transparent cursor-default" : ""}` +
                            " " +
                            `${!isSearchable && !open ? "cursor-pointer" : ""}` +
                            " " +
                            (isRtl ? "rtl:text-right" : "text-left") +
                            " " +
                            (showCount ? "pl-10" : "")
                        }
                        ref={inputRef}
                        placeholder={translate(placeholder ?? "")}
                        onClick={handleInputClick}
                        value={isOutFilter ? undefined : input}
                        defaultValue={isOutFilter ? defaultValue : undefined}
                        onChange={(e) => handleInputChange(e.target.value)}
                    />
                    {showSearchIcon && !isLoading && !isError && (
                        <div className={"absolute  top-1/2 -translate-y-1/2 text-gray-400" + " " + (isRtl ? "left-4" : "right-4")}>
                            {<FaSearch />}
                        </div>
                    )}
                    {showSearchIcon && isLoading && !isError && (
                        <div className={"absolute top-1/2 -translate-y-1/2 text-gray-400" + " " + (isRtl ? "left-4" : "right-4")}>
                            <div className="w-5">
                                <SpineComponent />
                            </div>
                        </div>
                    )}
                    {showSearchIcon && isError && (
                        <div className={"absolute top-1/2 -translate-y-1/2 text-red-500 text-xl" + " " + (isRtl ? "left-4" : "right-4")}>
                            {<MdErrorOutline />}
                        </div>
                    )}
                    {showCount && (
                        <div className={"absolute top-2 text-slate-400" + " " + (isRtl ? "left-2" : "right-2")}>
                            <span>{filteredItems?.length ?? 0}</span>
                            {showCount && isSearchable && (
                                <span> / {items?.length ?? 0}</span>
                            )}
                        </div>
                    )}
                </div>
                {showViewButton && (
                    <button
                        className={"w-1/6 border-gray-400 !rtl:border-l rtl:border-r"}
                        onClick={handleButtonClick}
                    >
                        {isLoading ? (
                            <div className="w-6 h-6 mx-auto">
                                <SpineComponent />
                            </div>
                        ) : (
                            translate(
                                open || (!!selectedItem && isOutFilter)
                                    ? TranslateConstants.CANCEL
                                    : TranslateConstants.VIEW
                            )
                        )}
                    </button>
                )}
            </div>
            {open && (
                <div className="absolute w-full mt-[.2rem] bg-base-100 z-50">
                    <ListComponent
                        padding="0"
                        space="0"
                        isBorder={true}
                        showBottomBorder={true}
                        height="max-h-32 bg-base-100"
                    >
                        {isLoading && (
                            <div className="flex items-center h-28 justify-center font-normal">
                                <div className="w-12">
                                    <SpineComponent />
                                </div>
                            </div>
                        )}
                        {!filteredItems?.length && !isLoading && (
                            <div className="flex items-center h-28 justify-center font-normal font-tajawal-bold">
                                {translate(TranslateConstants.NO_DATA_FOUND)}
                            </div>
                        )}
                        {!!filteredItems?.length &&
                            !isLoading &&
                            filteredItems?.map((el, index) => (
                                <ListTileItemComponent
                                    key={index}
                                    title={titleSelector ? titleSelector(el) : `${el}`}
                                    subTitle={subtitleSelector ? subtitleSelector(el) : undefined}
                                    padding={
                                        "px-2 py-2.5" +
                                        " " +
                                        (index === filteredItems.length - 1 ? "border-none" : "")
                                    }
                                    bottomBorderOnly={true}
                                    rounded={false}
                                    onClick={() => handleOnItemClick(el)}
                                    titleClassName="!font-normal"
                                />
                            ))}
                    </ListComponent>
                </div>
            )}
        </div>
    );
};

export default DropDownSearchComponent;
