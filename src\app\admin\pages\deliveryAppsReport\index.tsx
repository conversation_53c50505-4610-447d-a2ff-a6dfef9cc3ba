import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminDeliveryAppsReportTableHeaders } from "./AdminDeliveryAppsReportsConstants";
import { IOrderModel } from "../../../../common/models/OrderModel";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import AdminDeliveryAppsReportTotalInfoFeature from "./features/AdminDeliveryAppsReportTotalInfoFeature";
import useFetch from "../../../../common/asyncController/useFetch";
import { EndPointsConstants } from "../../../../common/constants/ApiConstants";
import { OrdersApiRepo } from "../../../../common/repos/api/OrdersApiRepo";
import { DeliveryAppsApiRepo } from "../../../../common/repos/api/DeliveryAppsApiRepo";
import { IDeliveryApp } from "../../../../common/interfaces";
import { DeliveryAppsHelper } from "../../../../common/helpers/DeliveryAppsHelper";
import { useMemo } from "react";

const AdminDeliveryAppsReportPage = () => {
    const { isXs, isSm } = useScreenSize();

    const onDate = (
        startDate: Date = new Date(),
        endDate: Date = new Date(),
        deliveryApp?: IDeliveryApp
    ) => {
        const start = DateUtils.getStartOfDayNumber(startDate);
        const end = DateUtils.getEndOfDayNumber(endDate);
        refetchOrderSum(start, end, deliveryApp);
        refetchOrders(start, end, deliveryApp);
    };

    const {
        data: deliverApps,
        isLoading: deliverAppsLoading,
        isError: deliverAppsError,
    } = useFetch(
        EndPointsConstants.DELIVERY_APPS, DeliveryAppsApiRepo.getDeliveryApps,
        {
            resetOnUnmount: true,
            onFirstSuccess({ data }) {
                const now = new Date();
                const activeApps = DeliveryAppsHelper.getActiveDeliveryApps(data);
                activeApps?.[0] && onDate(now, now, activeApps?.[0]);
            },
        }
    );

    const deliverAppsItems = useMemo(() => {
        return deliverApps ? DeliveryAppsHelper.getActiveDeliveryApps(deliverApps) : [];
    }, [deliverApps]);

    const {
        data: orderSum,
        isLoading: orderSumLoading,
        isError: orderSumError,
        refetch: refetchOrderSum,
    } = useFetch(
        "report-deliveryApps-" + EndPointsConstants.ORDERS_SUM,
        (startTime, endTime, deliveryApp) => OrdersApiRepo.getSum(
            startTime,
            endTime,
            undefined,
            deliveryApp?.name
        ),
        { autoFetchIfEmpty: false }
    );

    const {
        data: orders,
        isLoading: ordersLoading,
        isError: ordersError,
        refetch: refetchOrders,
    } = useFetch(
        "report-deliveryApps-" + EndPointsConstants.ORDERS,
        (startTime, endTime, deliveryApp) => OrdersApiRepo.getOrders(
            startTime,
            endTime,
            undefined,
            undefined,
            deliveryApp?.name
        ),
        { autoFetchIfEmpty: false }
    );

    const onDownload = () => {};

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent
                showSearch={true}
                onDate={onDate}
                items={deliverAppsItems}
                isSearchLoading={deliverAppsLoading}
                isSearchError={deliverAppsError}
                titleSelector={(item) => TranslateHelper.t(item.name)}
                defaultValue={TranslateHelper.t(deliverAppsItems?.[0]?.name)}
                defaultItem={deliverAppsItems?.[0]}
                isSearchable={false}
                onDownload={onDownload}
                isDownloadDisabled={!orders?.length || !orderSum}
            />
            <AdminDeliveryAppsReportTotalInfoFeature orderSum={orderSum} />
            <StatusComponent
                isEmpty={!orders?.length}
                isLoading={orderSumLoading || ordersLoading}
                isError={orderSumError || ordersError}
                height={isXs ? 17.5 : isSm ? 14.6 : 15.6}
            >
                <TableComponent
                    headers={AdminDeliveryAppsReportTableHeaders}
                    items={orders || []}
                    selectors={(item: IOrderModel) => [
                        item.invoiceNumber,
                        item.subTotal.toFixed(2),
                        item.discount.toFixed(2),
                        item.vat.toFixed(2),
                        item.total.toFixed(2),
                        item.deliveryAppFee.toFixed(2),
                        item.totalDue.toFixed(2),
                    ]}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminDeliveryAppsReportPage;
