import { IOrderProductSumModel } from "../../../../common/models/OrderProductSumModel";
import { IOrderSumModel } from "../../../../common/models/OrderSumModel";
import { PdfMakeUtils } from "../../../../common/printers/PdfMakeUtils";
import { TobaccoReportPdfPrinterContent } from "../../../../common/printers/slices/tobaccoReport/TobaccoReportPdfPrinterContent";
import { ITobaccoReportPdfPrinterModel } from "../../../../common/printers/slices/tobaccoReport/TobaccoReportPdfPrinterModel";
import { DateUtils } from "../../../../common/utils/DateUtils";

export class AdminTobaccoTaxReportService {
    static async handleDownload(
        dates: { startDate: Date; endDate: Date },
        ordersProducts: IOrderProductSumModel[] | undefined,
        ordersProductsSum: IOrderSumModel | undefined
    ) {
        const model: ITobaccoReportPdfPrinterModel = {
            startDate: dates.startDate,
            endDate: dates.endDate,
            items: ordersProducts?.map((el) => ({
                invoiceNumber: el.invoiceNumber,
                name: el.name,
                price: el.price,
                quantity: el.quantity,
                discount: el.discount,
                subTotal: el.subTotal,
                tobaccoTax: el.tobaccoTax,
                vat: el.vat,
                total: el.total,
                startTime: DateUtils.format(el.startTime),
            })),
            totals: {
                subTotal: ordersProductsSum?.subTotal ?? 0,
                discount: ordersProductsSum?.discount ?? 0,
                tobaccoTax: ordersProductsSum?.tobaccoTax ?? 0,
                vat: ordersProductsSum?.vat ?? 0,
                total: ordersProductsSum?.total ?? 0,
            },
        };
        PdfMakeUtils.download(TobaccoReportPdfPrinterContent(model), "Tobacco Report", { isLandScape: true });
    }
}
