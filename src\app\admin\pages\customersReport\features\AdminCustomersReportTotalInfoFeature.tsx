import { FC } from "react";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import ReportTotalInfoComponent from "../../../components/AdminReportTotalInfoComponent";
import { IOrderSumModel } from "../../../../../common/models/OrderSumModel";

interface IProps {
    orderSum?: IOrderSumModel;
}

const AdminCustomersReportTotalInfoFeature: FC<IProps> = ({ orderSum }) => {
    return (
        <ReportTotalInfoComponent
            items={[
                { text: TranslateConstants.ORDERS_COUNT, value: orderSum?.count, fixedVal: 0 },
                { text: TranslateConstants.TAX, value: orderSum?.vat },
                { text: TranslateConstants.TOTAL, value: orderSum?.total },
                {
                    text: TranslateConstants.CASH,
                    value: orderSum?.cash,
                    isHiddenInSm: true,
                },
                {
                    text: TranslateConstants.NETWORK,
                    value: orderSum?.network,
                    isHiddenInSm: true,
                },
            ]}
        />
    );
};

export default AdminCustomersReportTotalInfoFeature;
