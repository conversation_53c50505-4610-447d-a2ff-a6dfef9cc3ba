import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import {
    DeliveryAppEnum,
    OrderStatusEnum,
    OrderTypeEnum,
    ProductSizeTypeEnum,
} from "../../../../common/enums/DataEnums";
import { PrintersHelper } from "../../../../common/helpers/PrintersHelper";
import { ShiftHelper } from "../../../../common/helpers/ShiftHelper";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { ICategoryModel } from "../../../../common/models/CategoryModel";
import {
    IProductModel,
    IProductSizeModel,
} from "../../../../common/models/ProductModel";
import { ITableModel } from "../../../../common/models/TableModel";
import { IActions } from "../../../../common/redux/data/useActions";
import { debug, isOnlineState } from "../../../../common/utils/CommonUtils";
import { PriceUtils } from "../../../../common/utils/PriceUtils";
import { IPosOrder } from "../../interface";
import { IPosActions } from "../../redux/usePosActions";
import { PosOrderRepo } from "../../repo/PosOrderRepo";
import { PosPrintersRepo } from "../../repo/PosPrintersRepo";
import { PosShiftRepo } from "../../repo/PosShiftRepo";
import { PosShiftService } from "../../services/PosShiftService";
import { PosHomeUtils } from "./PosHomeUtils";

export class PosHomeService {
    static handleAddProductToOrder(
        product: IProductModel,
        posActions: IPosActions,
        order: IPosOrder,
        size?: IProductSizeModel
    ) {
        posActions.setOrderProduct({
            product,
            size,
            quantity: 1,
            productSizeType: !!size
                ? ProductSizeTypeEnum.MULTIPLE
                : ProductSizeTypeEnum.FIXED,
            name: (!!size ? `${product.name} - ${size.name}` : product.name).trim(),
            price: PriceUtils.getProductPrice(size ?? product, order),
            isSubjectToTobaccoTax: product.isSubjectToTobaccoTax,
        });
    }

    static handelSelectCategory(
        selectedCategory: ICategoryModel,
        posActions: IPosActions
    ) {
        posActions.setHome({ selectedCategory, view: "products" });
    }

    static handleSelectDeliveryApp(
        deliveryApp: DeliveryAppEnum,
        posActions: IPosActions,
        order: IPosOrder
    ) {
        if (order.deliveryApp !== deliveryApp)
            posActions.setOrder({ products: [], selectedDiscount: undefined });

        posActions.setOrder({ deliveryApp, table: undefined });
        posActions.setHome({ view: "categories" });
    }

    static handleTableSelect(table: ITableModel, posActions: IPosActions) {
        posActions.setOrder({ table, deliveryApp: undefined });
        posActions.setHome({ view: "categories" });
    }

    static handleHomeViewForOrderType(order: IPosOrder, posActions: IPosActions) {
        switch (order.type) {
            case OrderTypeEnum.TAKE_AWAY:
                posActions.setHome({ view: "categories" });
                break;
            case OrderTypeEnum.DINE_IN:
                if (order.status === OrderStatusEnum.PENDING) {
                    posActions.setHome({ view: "tables" });
                }
                break;
            case OrderTypeEnum.DELIVERY_APP:
                posActions.setHome({ view: "delivery_apps" });
                break;
        }
    }

    static handleBackButton(
        view: string,
        order: IPosOrder,
        posActions: IPosActions
    ) {
        if (view === "products") {
            posActions.setHome({ view: "categories", selectedCategory: undefined });
        } else if (view === "categories" && order.type === OrderTypeEnum.DINE_IN) {
            posActions.setHome({ view: "tables" });
        } else if (view === "categories" && order.type === OrderTypeEnum.DELIVERY_APP) {
            posActions.setHome({ view: "delivery_apps" });
        }
    }

    static openDrawer = async () => {
        try {
            const printerId = PrintersHelper.getDefaultPrinter()?.printer;

            if (!printerId) {
                ToastHelper.error(TranslateConstants.ERROR_PRINTER_SETTINGS);
                return;
            }

            await PosPrintersRepo.printEmpty(printerId);
        } catch (error) {
            debug(`PosOrderService [openDrawer] Error: ${error}`);
            ToastHelper.error();
        }
    };

    static async handleShift(
        amount: number,
        posActions: IPosActions,
        actions: IActions
    ) {
        try {
            if (amount < 0) {
                ToastHelper.error(TranslateConstants.INVALID_SHIFT_AMOUNT);
                return;
            }
            actions.setLoading(true);
            const isShiftOpen = ShiftHelper.isOpen();
            if (isShiftOpen) {
                await this.handleCloseShift(amount, posActions);
            } else {
                const shift = ShiftHelper.start(amount);
                await PosShiftRepo.startShift(shift);
                posActions.setHome({ isShiftOpen: true });
            }
        } catch (error) {
            debug(`PosHomeService [handleShift] Error: ${error}`);
            ToastHelper.error();
        } finally {
            actions.closeModal();
            actions.setLoading(false);
        }
    }

    private static async handleCloseShift(
        endAmount: number,
        posActions: IPosActions
    ) {
        try {
            const { count } = await PosOrderRepo.getAndCountOrders([
                `status`,
                "==",
                OrderStatusEnum.IN_PROGRESS,
            ]);

            if (count > 0) {
                ToastHelper.error(
                    TranslateConstants.ERROR_CLOSE_SHIFT_WITH_PENDING_ORDERS
                );
                return;
            }

            const shift = await PosHomeUtils.getCurrentShiftData(endAmount);
            const shiftWithDeliveryApps = await PosHomeUtils.getCurrentSiftDataWithDeliveryApps(
                endAmount
            );

            await PosShiftRepo.endShift(shift);
            ShiftHelper.close();
            posActions.setHome({ isShiftOpen: false });
            await PosPrintersRepo.printShift(shiftWithDeliveryApps);
            if (isOnlineState) {
                await PosShiftService.endDay();
                ToastHelper.success(TranslateConstants.END_DAY_SUCCESSFULLY);
            } else {
                ToastHelper.warning(TranslateConstants.NO_INTERNET_CAN_NOT_SYNC_DATA);
            }
        } catch (error) {
            debug(`PosHomeService [handleCloseShift] Error: ${error}`);
            throw error;
        }
    }
}
