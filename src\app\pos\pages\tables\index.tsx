import { useNavigate } from "react-router-dom";
import ListComponent from "../../../../common/components/ListComponent";
import { RoutsConstants } from "../../../../common/constants/RoutesConstants";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { TableStatusEnum } from "../../../../common/enums/DataEnums";
import { useTranslate } from "../../../../common/hooks/useTranslate";
import { ITableModel } from "../../../../common/models/TableModel";
import { useAppSelector } from "../../../../common/redux/store";
import PosBackButtonComponent from "../../components/PosBackButtonComponent";
import PosTableCardComponent from "../../components/PosTableCardComponent";
import { posTablesSelector } from "../../redux/selector";
import usePosActions from "../../redux/usePosActions";

const PosTablesPage = () => {
    const posActions = usePosActions();
    const { translate } = useTranslate();
    const tables = useAppSelector(posTablesSelector);
    const navigate = useNavigate();

    const handleOnTableClick = (table: ITableModel) => {
        if (!table.order) return;
        posActions.setHome({ view: "categories" });
        posActions.setOrder(table.order);
        navigate(RoutsConstants.pos.home.fullPath);
    };

    return (
        <div className="p-4 flex flex-col gap-2">
            <div className="font-tajawal-bold text-2xl">
                {translate(TranslateConstants.TABLES)}
            </div>
            <ListComponent
                cols={6}
                calcHeight={12}
                allowScrollBar={false}
                padding="0 p-2"
                isBorder={true}
            >
                {tables.map((table, index) => {
                    if (table.status !== TableStatusEnum.OCCUPIED) return null;
                    return (
                        <PosTableCardComponent
                            key={index}
                            {...table}
                            onClick={() => handleOnTableClick(table)}
                        />
                    );
                })}
            </ListComponent>
            <PosBackButtonComponent />
        </div>
    );
};

export default PosTablesPage;
