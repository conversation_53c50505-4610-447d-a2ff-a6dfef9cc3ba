import { lazy } from 'react'

const NotFoundComponent = lazy(() => import('../components/NotFoundComponent'))
const AuthPage = lazy(() => import('../../app/auth'))
const RolePage = lazy(() => import('../../app/role'))
const PosLayout = lazy(() => import('../../app/pos/layout'))
const PosHomePage = lazy(() => import('../../app/pos/pages/home'))
const PosTablesPage = lazy(() => import('../../app/pos/pages/tables'))
const PosInvoicesPage = lazy(() => import('../../app/pos/pages/invoices'))
const PosCustomersPage = lazy(() => import('../../app/pos/pages/customers'))
const PosSettingsPage = lazy(() => import('../../app/pos/pages/settings'))
const AdminLayout = lazy(() => import('../../app/admin/layout'))
const AdminCategoriesPage = lazy(() => import('../../app/admin/pages/categories'))
const AdminDiscountsPage = lazy(() => import('../../app/admin/pages/discounts'))
const AdminTablesPage = lazy(() => import('../../app/admin/pages/tables'))
const AdminProductsPage = lazy(() => import('../../app/admin/pages/products'))
const AdminSalesReportPage = lazy(() => import('../../app/admin/pages/salesReport'))
const AdminMostSellingProductsReportPage = lazy(() => import('../../app/admin/pages/mostSellingProductsReport'))
const AdminShiftReportPage = lazy(() => import('../../app/admin/pages/shiftReport'))
const AdminTobaccoTaxReportPage = lazy(() => import('../../app/admin/pages/tobaccoTaxReport'))
const AdminSettingsPage = lazy(() => import('../../app/admin/pages/settings'))
const AdminCustomersPage = lazy(() => import('../../app/admin/pages/customers'))
const AdminCustomersReportPage = lazy(() => import('../../app/admin/pages/customersReport'))
const AdminAdditionsPage = lazy(() => import('../../app/admin/pages/additions'))
const AdminAdditionReportPage = lazy(() => import('../../app/admin/pages/additionsReport'))

export const RoutsConstants = {
    notFound: {
        path: "*",
        component: NotFoundComponent
    },
    auth: {
        path: "/",
        component: AuthPage
    },
    role: {
        path: "/role",
        component: RolePage
    },
    adminLayout: {
        path: "/admin/*",
        component: AdminLayout,
    },
    posLayout: {
        path: "/pos/*",
        component: PosLayout,
    },
    admin: {
        categories: {
            path: "/categories",
            fullPath: "/admin/categories",
            component: AdminCategoriesPage
        },
        additions: {
            path: "/additions",
            fullPath: "/admin/additions",
            component: AdminAdditionsPage
        },
        products: {
            path: "/products",
            fullPath: "/admin/products",
            component: AdminProductsPage
        },
        tables: {
            path: "/tables",
            fullPath: "/admin/tables",
            component: AdminTablesPage
        },
        discounts: {
            path: "/discounts",
            fullPath: "/admin/discounts",
            component: AdminDiscountsPage
        },
        customers: {
            path: "/customers",
            fullPath: "/admin/customers",
            component: AdminCustomersPage
        },
        shiftReport: {
            path: "/shift_report",
            fullPath: "/admin/shift_report",
            component: AdminShiftReportPage
        },
        tobaccoTaxReport: {
            path: "/tobacco_tax_report",
            fullPath: "/admin/tobacco_tax_report",
            component: AdminTobaccoTaxReportPage
        },
        salesReport: {
            path: "/sales_report",
            fullPath: "/admin/sales_report",
            component: AdminSalesReportPage
        },
        customersReport: {
            path: "/customers_report",
            fullPath: "/admin/customers_report",
            component: AdminCustomersReportPage
        },
        mostSellingProductsReport: {
            path: "/most_selling_products_report",
            fullPath: "/admin/most_selling_products_report",
            component: AdminMostSellingProductsReportPage
        },
        additionsReport: {
            path: "/additions_report",
            fullPath: "/admin/additions_report",
            component: AdminAdditionReportPage
        },
        settings: {
            path: "/settings",
            fullPath: "/admin/settings",
            component: AdminSettingsPage
        },
    },
    pos: {
        home: {
            path: "/home",
            fullPath: "/pos/home",
            component: PosHomePage
        },
        tables: {
            path: "/tables",
            fullPath: "/pos/tables",
            component: PosTablesPage
        },
        invoices: {
            path: "/invoices",
            fullPath: "/pos/invoices",
            component: PosInvoicesPage
        },
        customers: {
            path: "/customers",
            fullPath: "/pos/customers",
            component: PosCustomersPage
        },
        settings: {
            path: "/settings",
            fullPath: "/pos/settings",
            component: PosSettingsPage
        },
    }
}