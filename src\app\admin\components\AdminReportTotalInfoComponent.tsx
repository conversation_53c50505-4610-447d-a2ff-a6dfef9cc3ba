import { FC, useMemo } from "react";
import { useTranslate } from "../../../common/hooks/useTranslate";
import useScreenSize from "../../../common/hooks/useScreenSize";

const InfoBoxComponent = ({
    name,
    val,
    hideInSm,
    disable,
    fixedVal = 2,
}: {
    name: string;
    val?: number;
    hideInSm?: boolean;
    disable?: boolean;
    fixedVal?: number;
}) => {
    const { translate } = useTranslate();
    return (
        <div
            className={
                "text-white dark:text-white flex flex-col justify-between px-1 sm:px-2 py-1 sm:py-2 items-center flex-1 rounded" +
                " " +
                (hideInSm ? "hidden sm:flex" : "") +
                " " +
                ((!val || disable) ? "bg-slate-400" : "bg-[#418dd9]")
            }
        >
            <span>{translate(name)}</span>
            <span>{(!val || disable) ? "0.00" : val.toFixed(fixedVal)}</span>
        </div>
    );
};

interface IProps {
    items: {
        text: string;
        value?: number;
        isHiddenInSm?: boolean;
        fixedVal?: number;
    }[];
    disable?: boolean;
}

const ReportTotalInfoComponent: FC<IProps> = ({ items, disable }) => {
    const { isXs } = useScreenSize();
    const cols = useMemo(() => {
        const count = items.length > 3 ? items.length : 3;
        return isXs ? 3 : count;
    }, [items]);

    return (
        <div
            className="grid gap-1 sm:gap-2 font-tajawal-bold text-sm lg:text-base"
            style={{ gridTemplateColumns: `repeat(${cols}, minmax(0, 1fr))` }}
        >
            {items.map((item, index) => (
                <InfoBoxComponent
                    key={index}
                    name={item.text}
                    val={item.value}
                    hideInSm={item.isHiddenInSm}
                    disable={disable}
                    fixedVal={item.fixedVal}
                />
            ))}
        </div>
    );
};

export default ReportTotalInfoComponent;
