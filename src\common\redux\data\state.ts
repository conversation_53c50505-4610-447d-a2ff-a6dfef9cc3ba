import { IModalProps } from "../../components/ModalComponent";
import IState from "./interface";

export const initialModalState: IModalProps = {
    show: false,
    component: null,
    title: undefined,
    size: 'md',
    submitButton: undefined,
    showButtons: true,
    onClose: undefined,
    closeOnBlur: false,
    closButtonClassName: ''
}

const InitialState: IState = {
    modal: initialModalState,
    loading: false,
    updatesCount: {
        productsUpdatesCount: 0,
        categoriesUpdatesCount: 0,
        tablesUpdatesCount: 0,
        discountsUpdatesCount: 0,
        customersUpdatesCount: 0,
        additionsUpdatesCount: 0,
        deliveryAppsUpdatesCount: 0,
    }
}

export default InitialState;