import { FC, useMemo } from "react";
import ButtonComponent from "../../../../../common/components/ButtonComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { IPosOrder } from "../../../interface";
import { orderUtils } from "../../../../../common/utils/OrderUtils";
import useActions from "../../../../../common/redux/data/useActions";
import PosOrderPaymentModal from "../modals/PosOrderPaymentModal";
import {
    OrderStatusEnum,
    OrderTypeEnum,
} from "../../../../../common/enums/DataEnums";
import { PosOrderService } from "../PosOrderService";
import usePosActions from "../../../redux/usePosActions";

interface IProps {
    order: IPosOrder;
    isProductsEqual: boolean;
}

const PosOrderSubmitButtonComponent: FC<IProps> = ({
    order,
    isProductsEqual,
}) => {
    const actions = useActions();
    const posActions = usePosActions();

    const { total } = useMemo(() => {
        return orderUtils.getPosOrderData(order);
    }, [order]);

    const isDisabled = useMemo(() => {
        return (
            !order.products.length ||
            (
                order.type === OrderTypeEnum.DINE_IN &&
                (order.status !== OrderStatusEnum.IN_PROGRESS || !isProductsEqual)
            )
        );
    }, [order]);

    const handleOrderSubmit = () => {
        if (order.type === OrderTypeEnum.DELIVERY_APP) {
            PosOrderService.handleSubmitOrder(order, 0, 0, actions, posActions);
            return;
        }

        actions.openModal({
            component: <PosOrderPaymentModal />,
            size: "lg",
            title: TranslateConstants.PAY,
            showButtons: false,
        });
    };

    return (
        <ButtonComponent
            text={TranslateConstants.PAY}
            subText={total.toFixed(2)}
            className={
                "py-6 !rounded-none" +
                " " +
                (isDisabled ? "border-r border-slate-700" : "border-t")
            }
            textSize="text-lg"
            isDisabled={isDisabled}
            onClick={handleOrderSubmit}
        />
    );
};

export default PosOrderSubmitButtonComponent;
