import ButtonComponent from "../../../../../common/components/ButtonComponent";
import DividerComponent from "../../../../../common/components/DividerComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { SlScreenDesktop } from "react-icons/sl";
import { RiLogoutCircleRLine } from "react-icons/ri";
import { useTranslate } from "../../../../../common/hooks/useTranslate";
import PosSyncButtonComponent from "../../../components/PosSyncButtonComponent";
import PosEndDayButtonComponent from "../../../components/PosEndDayButtonComponent";
import { RoleService } from "../../../../role/RoleService";
import { useNavigate } from "react-router-dom";
import { AuthService } from "../../../../auth/AuthService";
import { MdOutlineGTranslate } from "react-icons/md";
import { ShowLanguageModal } from "../../../../../common/components/LanguageComponent";

const PosQuickSettingsFeature = () => {
    const { translate } = useTranslate();
    const navigate = useNavigate();

    const handleOnDashboardClick = () => RoleService.redirectToRolePage(navigate);
    const handleOnSignOutClick = () => AuthService.logout(navigate);

    return (
        <>
            <div className="text-2xl font-tajawal-bold">
                {translate(TranslateConstants.QUICK_SETTINGS)}
            </div>
            <DividerComponent />
            <ButtonComponent
                text={TranslateConstants.DASHBOARD}
                iconComponent={<SlScreenDesktop className="text-xl" />}
                bgColor="base-200"
                isCentered={false}
                className="flex-row-reverse py-3 rounded-lg"
                textColor="black"
                isBorder={true}
                onClick={handleOnDashboardClick}
            />
            <PosSyncButtonComponent />
            <PosEndDayButtonComponent />
            <ButtonComponent
                text={TranslateConstants.LANGUAGE}
                iconComponent={<MdOutlineGTranslate className="text-xl" />}
                bgColor="base-200"
                isCentered={false}
                className="flex-row-reverse py-3 rounded-lg"
                textColor="black"
                isBorder={true}
                onClick={ShowLanguageModal}
            />
            <ButtonComponent
                text={TranslateConstants.SIGN_OUT}
                iconComponent={<RiLogoutCircleRLine className="text-xl" />}
                bgColor="warning"
                isCentered={false}
                className="flex-row-reverse py-3 mt-auto rounded-lg"
                onClick={handleOnSignOutClick}
            />
        </>
    );
};

export default PosQuickSettingsFeature;
