import { FC } from "react";
import { IAdminDeliveryAppInputs } from "../AdminDeliveryAppsInterface";
import { AdminDeliveryAppInputs } from "../AdminDeliveryAppsConstants";
import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import { DeliveryAppsApiRepo } from "../../../../../common/repos/api/DeliveryAppsApiRepo";
import { EndPointsConstants } from "../../../../../common/constants/ApiConstants";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { AdminDeliveryAppsValidation } from "../AdminDeliveryAppsValidation";
import useCustomState from "../../../../../common/hooks/useCustomState";
import { useTranslate } from "../../../../../common/hooks/useTranslate";
import { fixedNumber } from "../../../../../common/utils/numberUtils";

interface IProps {
    item?: IAdminDeliveryAppInputs;
}

const AdminDeliveryAppsModal: FC<IProps> = ({ item }) => {
    const { translate } = useTranslate();
    const [state, setState] = useCustomState<IAdminDeliveryAppInputs>(
        AdminDeliveryAppInputs,
        item
    );
    const updateDeliveryApp = useFlatMutate(
        DeliveryAppsApiRepo.editDeliveryApp,
        {
            updateCached: {
                key: EndPointsConstants.DELIVERY_APPS,
                operation: "updateOne",
            },
            closeModalOnSuccess: true,
        }
    );

    const onClick = () => {
        const isValid = AdminDeliveryAppsValidation.inputsValidation(state);
        if (!isValid) return;
        if (item) return updateDeliveryApp(state);
    };

    return (
        <>
            <form className="px-2">
                <InputComponent
                    type="number"
                    label={translate(TranslateConstants.PERCENTAGE)}
                    value={state.percentage.toString()}
                    onChange={(percentage) =>
                        setState({ ...state, percentage: fixedNumber(percentage) })
                    }
                />
            </form>
            <ModalButtonsComponent onClick={onClick} text={TranslateConstants.SAVE} />
        </>
    );
};

export default AdminDeliveryAppsModal;
