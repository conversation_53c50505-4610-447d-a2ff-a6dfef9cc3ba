import {
    APP_LOCAL_DB_COLLECTIONS,
    AppLocalDB,
} from "../../../common/config/localDB";
import { EndPointsConstants } from "../../../common/constants/ApiConstants";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { IDiscountModel } from "../../../common/models/DiscountModel";
import { debug } from "../../../common/utils/CommonUtils";

export class PosDiscountsRepo {
    static async getAndCacheDiscounts(updatedAt?: Date) {
        try {
            const res = await AxiosHelper.get<IDiscountModel[]>(
                EndPointsConstants.DISCOUNTS,
                { params: { updatedAt } }
            );

            if (!res.success) throw new Error(res.message);
            const data = res.data || [];

            if (!updatedAt) await AppLocalDB.set(APP_LOCAL_DB_COLLECTIONS.DISCOUNTS, data);
            else await AppLocalDB.add(APP_LOCAL_DB_COLLECTIONS.DISCOUNTS, data, true);
        } catch (error) {
            debug(`PosDiscountsRepo [getAndCacheDiscounts] Error: `, error);
            throw error;
        }
    }

    static async getDiscounts(activeOnly: boolean = false) {
        try {
            const res = await AppLocalDB.get<IDiscountModel>(
                APP_LOCAL_DB_COLLECTIONS.DISCOUNTS,
                { where: activeOnly ? [[["active", "==", true]]] : undefined }
            );

            if (!res) {
                throw new Error("No Discounts found");
            }

            return { data: res };
        } catch (error) {
            debug(`PosDiscountsRepo [getDiscounts] Error: `, error);
            throw error;
        }
    }
}
