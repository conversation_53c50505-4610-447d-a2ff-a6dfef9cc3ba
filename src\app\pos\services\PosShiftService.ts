import { IOrderBody } from "../../../common/interfaces/order";
import { IShiftBody } from "../../../common/interfaces/shift";
import { debug } from "../../../common/utils/CommonUtils";
import { PosOrderRepo } from "../repo/PosOrderRepo";
import { PosShiftRepo } from "../repo/PosShiftRepo";

export class PosShiftService {
    private static uploadShifts = async () => {
        try {
            let limit = 20;
            let skip = 0;

            let shifts = await PosShiftRepo.getShifts({ limit, skip });
            while (shifts.length) {
                const body: IShiftBody[] = shifts.map((el) => ({
                    shiftId: el.shiftId,
                    startAmount: el.startAmount || 0,
                    endAmount: el.endAmount || 0,
                    ordersCount: el.ordersCount || 0,
                    additionAmount: el.additionAmount || 0,
                    shortageAmount: el.shortageAmount || 0,
                    totalAmount: el.totalAmount || 0,
                    cashAmount: el.cashAmount || 0,
                    networkAmount: el.networkAmount || 0,
                    deliveryAppsAmount: el.deliveryAppsAmount || 0,
                    vatAmount: el.vatAmount || 0,
                    discountAmount: el.discountAmount || 0,
                    tobaccoTaxAmount: el.tobaccoTaxAmount || 0,
                    startTime: new Date(el.startTime).getTime(),
                    endTime: new Date(el.endTime).getTime(),
                }));
                await PosShiftRepo.uploadShifts(body);
                skip = shifts[shifts.length - 1].id;
                shifts = await PosShiftRepo.getShifts({ limit, skip });
            }
            await PosShiftRepo.deleteAllShifts();
        } catch (error) {
            debug(`PosShiftService [uploadShifts] Error: `, error);
            throw error;
        }
    };

    private static uploadOrders = async () => {
        try {
            let limit = 50;
            let skip = 0;

            let orders = await PosOrderRepo.getOrders({ limit, skip });
            while (orders.length) {
                const body: IOrderBody[] = orders.map((el) => ({
                    cash: el.cash || 0,
                    network: el.network || 0,
                    total: el.total || 0,
                    subTotal: el.subTotal || 0,
                    discount: el.discount || 0,
                    tobaccoTax: el.tobaccoTax || 0,
                    shiftId: el.shiftId,
                    vat: el.vat || 0,
                    tableId: el.table?.id,
                    customerId: el.customer?.id,
                    status: el.status,
                    type: el.type,
                    deliveryApp: el.deliveryApp,
                    deliveryAppFee: el.deliveryAppFee || 0,
                    totalDue: el.totalDue || 0,
                    orderProducts: el.products.map((pr) => ({
                        productId: pr.product.id,
                        quantity: pr.quantity,
                        price: pr.price,
                        name: pr.name,
                        isSubjectToTobaccoTax: pr.isSubjectToTobaccoTax ?? false,
                        subTotal: pr.subTotal ?? 0,
                        discount: pr.discount ?? 0,
                        tobaccoTax: pr.tobaccoTax ?? 0,
                        vat: pr.vat ?? 0,
                        total: pr.total ?? 0,
                        startTime: pr.startTime ?? new Date().getTime(),
                        orderProductAdditions: pr.additions?.map((ad) => ({
                            additionId: ad.addition.id,
                            quantity: ad.quantity,
                            price: ad.price,
                            name: ad.name,
                            subTotal: ad.subTotal ?? 0,
                            discount: ad.discount ?? 0,
                            vat: ad.vat ?? 0,
                            total: ad.total ?? 0,
                            startTime: ad.startTime ?? new Date().getTime(),
                        }))
                    })),
                    selectedDiscountId: el.selectedDiscount?.id,
                    invoiceNumber: el.invoiceNumber || "",
                    orderNumber: el.orderNumber || "",
                    startTime: new Date(el.startTime || "").getTime(),
                    endTime: new Date(el.endTime || "").getTime(),
                }));
                await PosOrderRepo.uploadOrders(body);
                skip = orders[orders.length - 1].id;
                orders = await PosOrderRepo.getOrders({ limit, skip });
            }
            await PosOrderRepo.deleteAllOrders();
        } catch (error) {
            debug(`PosShiftService [uploadOrders] Error: `, error);
            throw error;
        }
    };

    static endDay = async () => {
        try {
            await this.uploadShifts();
            await this.uploadOrders();
        } catch (error) {
            debug(`PosShiftService [endDay] Error: `, error);
            throw error;
        }
    };
}