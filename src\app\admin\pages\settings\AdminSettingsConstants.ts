import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import { IOrganizationSensitiveModel } from "../../../../common/models/OrganizationModel";
import { IAdminSettingsInputs } from "./AdminSettingsInterface";

export const AdminSettingsConstants = (
    organizationSensitives?: IOrganizationSensitiveModel
): IAdminSettingsInputs => {
    const organization = OrganizationHelper.getOrganization();

    return {
        name: organization?.name || "",
        nameEn: organization?.nameEn || "",
        subName: organization?.subName || "",
        subNameEn: organization?.subNameEn || "",
        taxNumber: organization?.taxNumber || "",
        registrationNumber: organization?.registrationNumber || "",
        mobile: organization?.mobile || "",
        address: organization?.address || "",
        password: "",
        logo: undefined,
        adminPassword: organizationSensitives?.adminPassword || "",
        posPassword: organizationSensitives?.posPassword || "",
        activateTobaccoTax: organization?.activateTobaccoTax || false,
        invoiceFooter: organization?.invoiceFooter || "",
    };
};
