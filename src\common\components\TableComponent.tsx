import { FC } from "react";
import { TranslateConstants } from "../constants/TranslateConstants";
import ButtonComponent, { IButtonComponentProps } from "./ButtonComponent";
import ListComponent from "./ListComponent";
import useActions from "../redux/data/useActions";
import { useTranslate } from "../hooks/useTranslate";
import logo from "/logo.svg";
import ToggleButtonComponent from "./ToggleButtonComponent";
import { isIOS } from "react-device-detect";

type CustomButton = Omit<IButtonComponentProps, "onClick"> & {
    onClick: (item: any) => void;
};

interface IThProps {
    header?: string;
    isCentered?: boolean;
    isBoarded?: boolean;
    headerClassName?: string;
}

const ThComponent: FC<IThProps> = ({
    header = "",
    isCentered = false,
    isBoarded = false,
    headerClassName = "",
}) => {
    return (
        <td
            className={
                "dark:text-white capitalize rounded-none" +
                " " +
                (isCentered ? "text-center" : "") +
                " " +
                (isBoarded ? "border border-gray-400" : "") +
                " " +
                (isIOS ? "bg-base-300" : "") +
                " " +
                headerClassName
            }
        >
            {header}
        </td>
    );
};

interface IProps {
    headers: string[];
    items: any[];
    selectors: (
        item: any
    ) => (string | number | undefined | React.ReactElement)[];
    onEdit?: (item: any) => void;
    onDelete?: (item: any) => void;
    onPrint?: (item: any) => void;
    showEditButton?: boolean;
    showDeleteButton?: boolean;
    showPrintButton?: boolean;
    showActiveButton?: boolean;
    activeSelector?: (item: any) => boolean;
    onActive?: (active: boolean, item: any) => void;
    showNumbering?: boolean;
    headSelector?: (item: any) => string;
    imageSelector?: (item: any) => string;
    isCentered?: boolean;
    isBoarded?: boolean;
    headerClassName?: string;
    cellClassName?: string;
    isTableFixed?: boolean;
    customButtons?: CustomButton[];
    disableOnEdit?: (item: any) => boolean;
    showActiveButtonBorder?: boolean;
}

const TableComponent: FC<IProps> = ({
    headers,
    items,
    selectors,
    onEdit,
    onDelete,
    onPrint,
    showEditButton = false,
    showDeleteButton = false,
    showPrintButton = false,
    showActiveButton = false,
    activeSelector,
    onActive,
    showNumbering = false,
    headSelector,
    imageSelector,
    isCentered = false,
    isBoarded = false,
    headerClassName = "",
    cellClassName = "",
    isTableFixed = false,
    customButtons,
    disableOnEdit,
    showActiveButtonBorder = true,
}) => {
    const { openModal, closeModal } = useActions();
    const { translate, isRtl } = useTranslate();

    const handleOnDelete = (item: any) => {
        openModal({
            component: (
                <div className="p-2">
                    {translate(TranslateConstants.DELETE_CONFIRMATION)}
                </div>
            ),
            title: translate(TranslateConstants.DELETE_ITEM),
            showButtons: true,
            submitButton: {
                text: translate(TranslateConstants.DELETE),
                onClick: () => {
                    onDelete && onDelete(item);
                    closeModal();
                },
            },
            size: "sm",
        });
    };

    return (
        <ListComponent allowScrollBar={false} padding="0">
            <table
                className={
                    "table table-compact" +
                    " " +
                    (isIOS ? "w-min" : "") +
                    " " +
                    (isTableFixed ? "md:table-fixed" : "") +
                    " " +
                    (isRtl ? "text-right" : "text-left")
                }
            >
                <thead className="sticky top-0">
                    <tr>
                        {(showNumbering || headSelector || imageSelector) && (
                            <ThComponent
                                header="#"
                                isCentered={isCentered}
                                isBoarded={isBoarded}
                                headerClassName={headerClassName}
                            />
                        )}
                        {headers.map((header, index) => {
                            return (
                                <ThComponent
                                    key={index}
                                    header={translate(header)}
                                    isCentered={isCentered}
                                    isBoarded={isBoarded}
                                    headerClassName={headerClassName}
                                />
                            );
                        })}
                        {(showEditButton ||
                            showDeleteButton ||
                            showPrintButton ||
                            showActiveButton) && (
                                <ThComponent
                                    isBoarded={isBoarded}
                                    headerClassName={
                                        headerClassName + (showActiveButton ? "flex justify-end" : "")
                                    }
                                    header={
                                        showActiveButton ? translate(TranslateConstants.STATUS) : ""
                                    }
                                />
                            )}
                    </tr>
                </thead>
                <tbody>
                    {items.map((item, i) => {
                        return (
                            <tr key={i} className="group">
                                {!!headSelector && (
                                    <td
                                        className={
                                            "rounded-none" +
                                            " " +
                                            (isCentered ? "text-center" : "") +
                                            " " +
                                            (isBoarded ? "border border-gray-400" : "") +
                                            " " +
                                            cellClassName
                                        }
                                    >
                                        {headSelector(item) ?? "-"}
                                    </td>
                                )}
                                {showNumbering && !headSelector && (
                                    <td
                                        className={
                                            "rounded-none" +
                                            " " +
                                            (isCentered ? "text-center" : "") +
                                            " " +
                                            (isBoarded ? "border border-gray-400" : "") +
                                            " " +
                                            cellClassName
                                        }
                                    >
                                        {i + 1}
                                    </td>
                                )}
                                {!!imageSelector && (
                                    <td
                                        className={
                                            "px-0 rounded-none" +
                                            " " +
                                            (isBoarded ? "border border-gray-400" : "") +
                                            " " +
                                            cellClassName
                                        }
                                    >
                                        <div className="w-10 h-10">
                                            <img
                                                className="h-full w-full rounded-full border"
                                                src={imageSelector(item) ?? logo}
                                                onError={(e) => {
                                                    e.currentTarget.src = logo;
                                                }}
                                            />
                                        </div>
                                    </td>
                                )}
                                {selectors(item).map((selector, j) => {
                                    return (
                                        <td
                                            key={j}
                                            className={
                                                "dark:text-white text-black rounded-none" +
                                                " " +
                                                (isCentered ? "text-center" : "") +
                                                " " +
                                                (isBoarded ? "border border-gray-400" : "") +
                                                " " +
                                                cellClassName
                                            }
                                        >
                                            {selector === "" ||
                                                selector === undefined ||
                                                selector === null
                                                ? "-"
                                                : selector}
                                        </td>
                                    );
                                })}
                                {(showEditButton ||
                                    showDeleteButton ||
                                    showPrintButton ||
                                    customButtons ||
                                    showActiveButton) && (
                                        <td
                                            className={
                                                "px-0 rounded-none" +
                                                " " +
                                                (isBoarded ? "border border-gray-400" : "") +
                                                " " +
                                                cellClassName
                                            }
                                        >
                                            <div className="flex justify-end gap-2">
                                                {customButtons &&
                                                    customButtons.map((button, index) => (
                                                        <ButtonComponent
                                                            key={index}
                                                            className="!w-16 !p-1"
                                                            {...button}
                                                            onClick={() => button.onClick && button.onClick(items[i])}
                                                        />
                                                    ))}
                                                {showPrintButton && (
                                                    <ButtonComponent
                                                        text={translate(TranslateConstants.PRINT)}
                                                        className="!w-16 !p-1"
                                                        onClick={() => onPrint && onPrint(items[i])}
                                                    />
                                                )}
                                                {showEditButton && (
                                                    <ButtonComponent
                                                        text={TranslateConstants.EDIT}
                                                        className="!w-16 !p-1"
                                                        onClick={() => onEdit && onEdit(items[i])}
                                                        isDisabled={disableOnEdit && disableOnEdit(items[i])}
                                                    />
                                                )}
                                                {showDeleteButton && (
                                                    <ButtonComponent
                                                        text={translate(TranslateConstants.DELETE)}
                                                        className="!w-16 !p-1"
                                                        bgColor="warning"
                                                        onClick={() => handleOnDelete(items[i])}
                                                    />
                                                )}
                                                {showActiveButton && (
                                                    <ToggleButtonComponent
                                                        value={activeSelector?.(item)}
                                                        onChange={(e) => onActive?.(e, item)}
                                                        className={`${showActiveButtonBorder ? (isRtl ? "border-r pr-2" : "border-l pl-2") : ""}`}
                                                    />
                                                )}
                                            </div>
                                        </td>
                                    )}
                            </tr>
                        );
                    })}
                </tbody>
            </table>
        </ListComponent>
    );
};

export default TableComponent;
