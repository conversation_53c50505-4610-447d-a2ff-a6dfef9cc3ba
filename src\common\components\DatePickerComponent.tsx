import Datepicker, { DateValueType } from "react-tailwindcss-datepicker";
import { FC, useState } from "react";
import { DateUtils } from "../utils/DateUtils";

interface IProps {
    onChange?: (start: Date, end: Date) => void;
    defaultValue?: DateValueType;
    label?: string;
    minDate?: Date;
}

const DatePickerComponent: FC<IProps> = ({
    onChange,
    defaultValue,
    label,
    minDate,
}) => {
    const [state, setState] = useState<DateValueType>({
        startDate: defaultValue?.startDate || null,
        endDate: defaultValue?.endDate || null,
    });

    const handleOnChange = (newValue: DateValueType) => {
        setState(newValue);
        if (onChange && newValue?.startDate && newValue?.endDate) {
            onChange(
                DateUtils.getStartOfDay(newValue.startDate),
                DateUtils.getEndOfDay(newValue.endDate)
            );
        }
    };

    return (
        <Datepicker
            value={state}
            onChange={handleOnChange}
            containerClassName="ltr-direction relative w-full sm:w-36 md:w-48"
            inputClassName="input input-bordered w-full rounded sm:w-36 md:w-48 border-gray-400 focus:outline-none h-[2.6rem] dark:text-white"
            showShortcuts={false}
            toggleIcon={(_) => (
                <span className="flex items-center justify-center text-black dark:text-white">
                    {label}
                </span>
            )}
            minDate={minDate}
            maxDate={new Date()}
            i18n="ar"
            showFooter={false}
            primaryColor="blue"
            startWeekOn="sat"
            placeholder="ي-ش-س"
            useRange={false}
            asSingle={true}
            configs={{
                shortcuts: {
                    today: "اليوم",
                    yesterday: "الأمس",
                    past: (period) => `الـ ${period} السابق`,
                    currentMonth: "الشهر الحالي",
                    pastMonth: "الشهر السابق",
                },
                footer: {
                    cancel: "إلغاء",
                    apply: "تطبيق",
                },
            }}
        />
    );
};

export default DatePickerComponent;
