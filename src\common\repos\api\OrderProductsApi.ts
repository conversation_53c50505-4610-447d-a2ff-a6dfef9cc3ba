import { EndPointsConstants } from "../../constants/ApiConstants";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IOrderProductSumModel } from "../../models/OrderProductSumModel";
import { IOrderSumModel } from "../../models/OrderSumModel";
import { debug } from "../../utils/CommonUtils";

export class OrderProductsApiRepo {
    static async sum(
        startTime?: number,
        endTime?: number,
        isTobaccoTax?: boolean
    ) {
        try {
            const res = await AxiosHelper.get<IOrderSumModel>(
                EndPointsConstants.ORDER_PRODUCTS_SUM,
                { params: { startTime, endTime, isTobaccoTax } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`OrderProductsApiRepo [sum] Error: `, error);
            throw error;
        }
    }

    static async getOrderProducts(
        startTime?: number,
        endTime?: number,
        isTobaccoTax?: boolean
    ) {
        try {
            const res = await AxiosHelper.get<IOrderProductSumModel[]>(
                EndPointsConstants.ORDER_PRODUCTS_GET,
                { params: { startTime, endTime, isTobaccoTax } }
            );
            if (!res.success) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`OrderProductsApiRepo [getOrders] Error: `, error);
            throw error;
        }
    }
}