import { FC } from "react";
import { IAdminAdditionInputs } from "../AdminAdditionsInterface";
import { AdminAdditionInputs } from "../AdminAdditionsConstants";
import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import { EndPointsConstants } from "../../../../../common/constants/ApiConstants";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { AdminAdditionsValidation } from "../AdminAdditionsValidation";
import useCustomState from "../../../../../common/hooks/useCustomState";
import ToggleButtonComponent from "../../../../../common/components/ToggleButtonComponent";
import { IAdditionModel } from "../../../../../common/models/AdditionsModel";
import { AdditionsApiRepo } from "../../../../../common/repos/api/AdditionsApiRepo";
import { fixedNumber } from "../../../../../common/utils/numberUtils";

interface IProps {
    item?: IAdditionModel;
}

const AdminAdditionsModal: FC<IProps> = ({ item }) => {
    const [state, setState, resetState] = useCustomState<IAdminAdditionInputs>(
        AdminAdditionInputs,
        item,
    );
    const addAddition = useFlatMutate(AdditionsApiRepo.addAddition, {
        updateCached: { key: EndPointsConstants.ADDITIONS, operation: "add" },
        afterEnd: () => resetState(),
    });
    const updateAddition = useFlatMutate(AdditionsApiRepo.updateAddition, {
        updateCached: {
            key: EndPointsConstants.ADDITIONS,
            operation: "update",
            selector: (data: IAdditionModel) => data.id,
        },
        closeModalOnSuccess: true,
    });

    const onClick = () => {
        const isValid = AdminAdditionsValidation.inputsValidation(state);
        if (!isValid) return;
        if (item) return updateAddition(item.id, state);
        addAddition(state);
    };

    return (
        <>
            <form className="px-2">
                <InputComponent
                    label={TranslateConstants.NAME}
                    onChange={(name) => setState({ ...state, name })}
                    value={state.name}
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.PRICE}
                    onChange={(price) => setState({ ...state, price: fixedNumber(price) })}
                    value={state.price.toString()}
                />
                <ToggleButtonComponent
                    onChange={(active) => setState({ ...state, active })}
                    label={TranslateConstants.ACTIVATION}
                    value={state.active}
                    className="mt-2"
                />
            </form>
            <ModalButtonsComponent text={TranslateConstants.SAVE} onClick={onClick} />
        </>
    );
};

export default AdminAdditionsModal;
