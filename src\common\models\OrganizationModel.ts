import { BaseModel } from ".";
import { OrganizationRoleEnum } from "../enums/DataEnums";
export interface IOrganizationSensitiveModel {
    adminPassword: string;
    posPassword: string;
}

export interface OrganizationModel extends BaseModel {
    name: string;
    email: string;
    nameEn?: string;
    mobile?: string;
    taxNumber?: string;
    registrationNumber?: string;
    address?: string;
    logo?: string;
    subName?: string;
    subNameEn?: string;
    role?: OrganizationRoleEnum;
    activateTobaccoTax?: boolean;
    invoiceFooter?: string;
}