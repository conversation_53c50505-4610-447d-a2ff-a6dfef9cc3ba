import { DeliveryAppEnum } from "../enums/DataEnums";
import { HttpStatus } from "../enums/HttpStatusEnums";
import { IKitchenPrinterItemModel } from "../models/KitchenPrinterItemModel";

export type IDataOperations = "add" | "remove" | "update" | "set";

export interface IResponse<T> {
    statusCode?: HttpStatus;
    message?: string;
    success?: boolean;
    access_token?: string;
    data?: T;
    error?: string;
    total?: number;
    page?: number;
    limit?: number;
    totalPages?: number;
}

export interface IPrinterData {
    category: string;
    categoryId: string;
    printer?: string;
}

export interface IKitchenPrinter {
    printerId: string;
    products: IKitchenPrinterItemModel[];
}

export interface IDeliveryApp {
    name: DeliveryAppEnum;
    image: string;
}