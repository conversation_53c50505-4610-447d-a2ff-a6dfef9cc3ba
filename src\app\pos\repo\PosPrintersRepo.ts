import axios from "axios";
import { debug } from "../../../common/utils/CommonUtils";
import { IPrinterModel } from "../../../common/models/PrinterModel";
import {
    EndPointsConstants,
    PrinterServerHost,
} from "../../../common/constants/ApiConstants";
import { PrintersUtils } from "../../../common/utils/PrintersUtil";
import { IPosOrder } from "../interface";
import { KitchenOrderStatusEnum } from "../../../common/enums/DataEnums";
import { PrintersHelper } from "../../../common/helpers/PrintersHelper";
import { IShiftPrinterBodyModel, IShiftPrinterModel } from "../../../common/models/ShiftPrinterModel";

export class PosPrintersRepo {
    static async getAllPrinters(): Promise<IPrinterModel[]> {
        try {
            const res = await axios.get<IPrinterModel[]>(
                `${PrinterServerHost}/${EndPointsConstants.GET_PRINTERS}`
            );
            return res.data;
        } catch (error) {
            debug(`PosSettingsRepo [getAllPrinters] Error: ${error}`);
            throw error;
        }
    }

    static async printKitchenOrder(
        order: IPosOrder,
        orderStatus: KitchenOrderStatusEnum
    ): Promise<void> {
        const orderToPrint = PrintersUtils.handelKitchenOrderPrint(
            order,
            orderStatus
        );

        try {
            for (const item of orderToPrint) {
                await axios.post(
                    `${PrinterServerHost}/${EndPointsConstants.PRINT_KITCHEN}`,
                    item
                );
            }
        } catch (error) {
            debug(`PosSettingsRepo [printKitchenOrder] Error: ${error}`);
            throw error;
        }
    }

    static async printInvoiceOrder(order: IPosOrder): Promise<void> {
        const orderToPrint = PrintersUtils.handleInvoiceOrderPrint(order);

        try {
            await axios.post(
                `${PrinterServerHost}/${EndPointsConstants.PRINT_INVOICE}`,
                orderToPrint
            );
        } catch (error) {
            debug(`PosSettingsRepo [printInvoiceOrder] Error: ${error}`);
            throw error;
        }
    }

    static async print(printerId: string, body: any[]): Promise<void> {
        try {
            await axios.post(`${PrinterServerHost}/${EndPointsConstants.PRINT}`, {
                printerId,
                body,
            });
        } catch (error) {
            debug(`PosSettingsRepo [printEmpty] Error: ${error}`);
            throw error;
        }
    }

    static async printEmpty(printerId: string): Promise<void> {
        try {
            await axios.post(`${PrinterServerHost}/${EndPointsConstants.PRINT}`, {
                printerId,
                body: [{ text: "" }],
            });
        } catch (error) {
            debug(`PosSettingsRepo [printEmpty] Error: ${error}`);
            throw error;
        }
    }

    static async printShift(shift: IShiftPrinterBodyModel): Promise<void> {
        try {
            const printerId = PrintersHelper.getDefaultPrinter()?.printer;
            if (!printerId) return;

            const body: IShiftPrinterModel = {
                printerId,
                body: shift,
            }

            await axios.post(
                `${PrinterServerHost}/${EndPointsConstants.PRINT_SHIFT}`,
                body
            );
        } catch (error) {
            debug(`PosSettingsRepo [printShift] Error: ${error}`);
            throw error;
        }
    }
}
