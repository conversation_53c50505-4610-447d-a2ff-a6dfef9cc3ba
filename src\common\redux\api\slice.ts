import { createApi } from "@reduxjs/toolkit/query/react";
import { customBaseQuery } from "./utils";
import { EndPointsConstants } from "../../constants/ApiConstants";
import { PosOrderRepo } from "../../../app/pos/repo/PosOrderRepo";
import { IOrderSumModel } from "../../models/OrderSumModel";
import { IOrderModel } from "../../models/OrderModel";
import { IShiftSumModel } from "../../models/ShiftsSumModel";
import { IShiftModel } from "../../models/ShiftModel";
import { IMostSellingProductsModel } from "../../models/MostSellingProductsModel";

export const appApi = createApi({
    reducerPath: "appApi",
    baseQuery: customBaseQuery,
    refetchOnReconnect: true,
    endpoints: (builder) => ({
        getPosOrders: builder.query({ queryFn: PosOrderRepo.getShiftOrders }),
        getOrdersSum: builder.query<IOrderSumModel, { startTime: number; endTime: number }>({
            query: (args) =>
                EndPointsConstants.ORDERS_SUM + `?startTime=${args.startTime}&endTime=${args.endTime}`,
        }),
        getOrders: builder.query<IOrderModel[], { startTime: number; endTime: number }>({
            query: (args) =>
                EndPointsConstants.ORDERS + `?startTime=${args.startTime}&endTime=${args.endTime}`,
        }),
        getShiftsSum: builder.query<IShiftSumModel, { startTime: number; endTime: number }>({
            query: (args) =>
                EndPointsConstants.SHIFTS_SUM + `?startTime=${args.startTime}&endTime=${args.endTime}`,
        }),
        getShifts: builder.query<IShiftModel[], { startTime: number; endTime: number }>({
            query: (args) =>
                EndPointsConstants.SHIFTS + `?startTime=${args.startTime}&endTime=${args.endTime}`,
        }),
        getOrderProductsSum: builder.query<IMostSellingProductsModel[], { startTime: number; endTime: number }>({
            query: (args) =>
                EndPointsConstants.ORDER_PRODUCTS_SUM_MANY + `?startTime=${args.startTime}&endTime=${args.endTime}`,
        }),
    }),
});

export const {
    useGetPosOrdersQuery,
    useGetOrdersSumQuery,
    useGetOrdersQuery,
    useGetShiftsSumQuery,
    useGetShiftsQuery,
    useGetOrderProductsSumQuery,
} = appApi;
