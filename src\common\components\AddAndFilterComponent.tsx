import { FC } from "react";
import { TranslateConstants } from "../constants/TranslateConstants";
import ButtonComponent from "./ButtonComponent";
import { TbReload } from "react-icons/tb";

interface IProps {
    onClick?: () => void;
    onReload?: () => void;
    onSave?: () => void;
    isSaveDisabled?: boolean;
    buttonsClassName?: string;
}

const AddAndFilterComponent: FC<IProps> = ({
    onClick,
    onReload,
    onSave,
    isSaveDisabled,
    buttonsClassName,
}) => {
    return (
        <div className="flex justify-between gap-5 w-full">
            <div className="flex-1 flex gap-2">
                {
                    onSave && (
                        <ButtonComponent
                            text={TranslateConstants.SAVE}
                            onClick={onSave}
                            className={"sm:!w-1/4 md:!w-1/5 lg:!w-1/6" + " " + buttonsClassName}
                            isDisabled={isSaveDisabled}
                        />
                    )
                }
                {
                    onClick && (
                        <ButtonComponent
                            text={TranslateConstants.ADD}
                            onClick={onClick}
                            className={"sm:!w-1/4 md:!w-1/5 lg:!w-1/6" + " " + buttonsClassName}
                        />
                    )
                }
                {
                    onReload && (
                        <ButtonComponent
                            text={TranslateConstants.RELOAD}
                            onClick={onReload}
                            className={"!w-28 sm:!w-1/4 md:!w-1/5 lg:!w-1/6 mr-auto sm:mr-0" + " " + buttonsClassName}
                            iconComponent={<TbReload />}
                            bgColor="deepPrimary"
                        />
                    )
                }
            </div>
        </div>
    );
}

export default AddAndFilterComponent;
