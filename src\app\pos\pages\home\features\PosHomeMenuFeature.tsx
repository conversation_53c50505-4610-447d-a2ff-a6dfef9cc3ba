import { MdTableRestaurant } from "react-icons/md";
import { LuNewspaper } from "react-icons/lu";
import { GrPowerShutdown } from "react-icons/gr";
import { IoSettingsOutline } from "react-icons/io5";
import { FaUser } from "react-icons/fa";
import { IoIosMore } from "react-icons/io";
import { IoFastFood } from "react-icons/io5";
import { TbPaperBag } from "react-icons/tb";
import { TbTruckDelivery } from "react-icons/tb";
import { PiHandDepositBold } from "react-icons/pi";
import { MdHistory } from "react-icons/md";
import { ImDrawer } from "react-icons/im";
import DropDownButtonComponent, {
    IDropDownMenuItem,
} from "../../../../../common/components/DropDownButtonComponent";
import IconButtonComponent from "../../../../../common/components/IconButtonComponent";
import { RoutsConstants } from "../../../../../common/constants/RoutesConstants";
import { OrderStatusEnum, OrderTypeEnum } from "../../../../../common/enums/DataEnums";
import { useTranslate } from "../../../../../common/hooks/useTranslate";
import { useAppSelector } from "../../../../../common/redux/store";
import { posHomeSelector, posOrderSelector, posTablesSelector } from "../../../redux/selector";
import usePosActions from "../../../redux/usePosActions";
import { PosHomeService } from "../PosHomeService";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import useActions from "../../../../../common/redux/data/useActions";
import PosHomeShiftModal from "../modals/PosHomeShiftModal";
import PosHomeCustomersModal from "../modals/PosHomeCustomersModal";
import { MdPeopleAlt } from "react-icons/md";
import { DeliveryAppsHelper } from "../../../../../common/helpers/DeliveryAppsHelper";

const PosHomeMenuFeature = () => {
    const posActions = usePosActions();
    const actions = useActions();
    const { translate } = useTranslate();
    const order = useAppSelector(posOrderSelector);
    const tables = useAppSelector(posTablesSelector);
    const { isShiftOpen } = useAppSelector(posHomeSelector);

    const orderTypeItems: IDropDownMenuItem[] = [
        {
            icon: <TbPaperBag />,
            text: translate(TranslateConstants.TAKE_AWAY),
            onClick: () => posActions.resetOrder(),
            active: order.type === OrderTypeEnum.TAKE_AWAY,
            isDisabled: order.status !== OrderStatusEnum.PENDING
        },
        {
            icon: <MdTableRestaurant />,
            text: translate(TranslateConstants.DINE_IN),
            onClick: () => posActions.resetOrder({ type: OrderTypeEnum.DINE_IN }),
            active: order.type === OrderTypeEnum.DINE_IN,
            isDisabled: !tables.length,
        },
        {
            icon: <TbTruckDelivery />,
            text: translate(TranslateConstants.DELIVER),
            isDisabled: true,
        },
        {
            icon: <PiHandDepositBold />,
            text: translate(TranslateConstants.DELIVERY_APPS),
            onClick: () => posActions.resetOrder({ type: OrderTypeEnum.DELIVERY_APP }),
            active: order.type === OrderTypeEnum.DELIVERY_APP,
            isDisabled: !DeliveryAppsHelper.hasDeliveryApps(),
        },
    ];

    const moreItems: IDropDownMenuItem[] = [
        {
            icon: <GrPowerShutdown className={isShiftOpen ? "text-red-700" : ""} />,
            text: translate(
                isShiftOpen
                    ? TranslateConstants.CLOSE_SHIFT
                    : TranslateConstants.OPEN_SHIFT
            ),
            onClick: () => {
                actions.openModal({
                    component: <PosHomeShiftModal />,
                    showButtons: false,
                    size: "sm",
                });
            },
        },
        {
            icon: <MdHistory />,
            text: translate(TranslateConstants.INVOICES),
            route: RoutsConstants.pos.invoices.fullPath,
        },
        {
            icon: <MdTableRestaurant />,
            text: translate(TranslateConstants.TABLES),
            route: RoutsConstants.pos.tables.fullPath,
        },
        {
            icon: <TbTruckDelivery />,
            text: translate(TranslateConstants.DELIVERY),
            isDisabled: true,
        },
        {
            icon: <MdPeopleAlt />,
            text: translate(TranslateConstants.CUSTOMERS),
            route: RoutsConstants.pos.customers.fullPath,
        },
        {
            icon: <LuNewspaper />,
            text: translate(TranslateConstants.REPORTS_X),
            isDisabled: true,
        },
        {
            icon: <LuNewspaper />,
            text: translate(TranslateConstants.REPORTS_Z),
            isDisabled: true,
        },
        {
            icon: <ImDrawer />,
            text: translate(TranslateConstants.OPEN_DRAWER),
            onClick: PosHomeService.openDrawer,
        },
        {
            icon: <IoSettingsOutline />,
            text: translate(TranslateConstants.SETTINGS),
            route: RoutsConstants.pos.settings.fullPath,
        },
    ];

    const onCustomerClick = () => actions.openModal({
        component: <PosHomeCustomersModal />,
        size: "sm",
        showButtons: false,
    })

    return (
        <div className="bg-slate-600 w-full h-[4.75rem] flex justify-between items-center">
            <DropDownButtonComponent
                icon={<IoFastFood />}
                text={TranslateConstants.ORDER_TYPE}
                rounded={false}
                bgColor="slate"
                className="border-l border-slate-700"
                textSize="text-lg"
                iconSize="text-3xl"
                items={orderTypeItems}
            />
            <IconButtonComponent
                icon={<FaUser />}
                text={TranslateConstants.CUSTOMERS}
                rounded={false}
                bgColor="slate"
                className="border-l border-slate-700"
                textSize="text-lg"
                iconSize="text-3xl"
                onClick={onCustomerClick}
                isDisabled={order.type === OrderTypeEnum.DELIVERY_APP}
            />
            <DropDownButtonComponent
                icon={<IoIosMore />}
                text={TranslateConstants.MORE}
                rounded={false}
                bgColor="slate"
                textSize="text-lg"
                iconSize="text-3xl"
                items={moreItems}
            />
        </div>
    );
};

export default PosHomeMenuFeature;
