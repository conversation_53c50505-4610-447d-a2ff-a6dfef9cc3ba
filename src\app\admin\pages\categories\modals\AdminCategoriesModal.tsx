import { FC } from "react";
import { IAdminCategoryInputs } from "../AdminCategoriesInterface";
import { AdminCategoryInputs } from "../AdminCategoriesConstants";
import { ICategoryModel } from "../../../../../common/models/CategoryModel";
import InputComponent from "../../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { ModalButtonsComponent } from "../../../../../common/components/ModalComponent";
import { CategoriesApiRepo } from "../../../../../common/repos/api/CategoriesApiRepo";
import { EndPointsConstants } from "../../../../../common/constants/ApiConstants";
import useFlatMutate from "../../../../../common/asyncController/useFlatMutate";
import { AdminCategoriesValidation } from "../AdminCategoriesValidation";
import useCustomState from "../../../../../common/hooks/useCustomState";
import UploadFileComponent from "../../../../../common/components/UploadImageComponent";
import ToggleButtonComponent from "../../../../../common/components/ToggleButtonComponent";

interface IProps {
    item?: ICategoryModel;
}

const AdminCategoriesModal: FC<IProps> = ({ item }) => {
    const [state, setState, resetState] = useCustomState<IAdminCategoryInputs>(
        AdminCategoryInputs,
        item,
        [], ["image"]
    );
    const addCategory = useFlatMutate(CategoriesApiRepo.addCategory, {
        updateCached: { key: EndPointsConstants.CATEGORIES, operation: "add" },
        afterEnd: () => resetState(),
    });
    const updateCategory = useFlatMutate(CategoriesApiRepo.updateCategory, {
        updateCached: {
            key: EndPointsConstants.CATEGORIES,
            operation: "update",
            selector: (data: ICategoryModel) => data.id,
        },
        closeModalOnSuccess: true,
    });

    const onClick = () => {
        const isValid = AdminCategoriesValidation.inputsValidation(state);
        if (!isValid) return;
        if (item) return updateCategory(item.id, state);
        addCategory(state);
    };

    return (
        <>
            <form className="px-2">
                <UploadFileComponent
                    label={TranslateConstants.IMAGE}
                    containerClassName="m-auto !h-28"
                    image={item?.image}
                    onFileChange={(image) => setState({ ...state, image })}
                    file={state.image}
                />
                <InputComponent
                    label={TranslateConstants.NAME}
                    onChange={(value) => setState({ ...state, name: value })}
                    value={state.name}
                />
                <ToggleButtonComponent
                    onChange={(active) => setState({ ...state, active })}
                    label={TranslateConstants.ACTIVATION}
                    value={state.active}
                    className="mt-2"
                />
            </form>
            <ModalButtonsComponent text={TranslateConstants.SAVE} onClick={onClick} />
        </>
    );
};

export default AdminCategoriesModal;
