import { useState } from "react";
import StatusComponent from "../../../../common/components/StatusComponent";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { useGetOrderProductsSumQuery } from "../../../../common/redux/api/slice";
import { DateUtils } from "../../../../common/utils/DateUtils";
import AdminReportControllerComponent from "../../components/AdminReportControllerComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { AdminMostSellingProductsReportTableHeaders } from "./AdminMostSellingProductsReportsConstants";
import { IMostSellingProductsModel } from "../../../../common/models/MostSellingProductsModel";
import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";

const AdminMostSellingProductsPage = () => {
    const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();
    const { isXs, isSm } = useScreenSize();
    const [dateRange, setDateRange] = useState({
        startTime: DateUtils.getStartOfDayNumber(),
        endTime: DateUtils.getEndOfDayNumber(),
    });

    const {
        data: ordersProducts,
        isFetching: ordersProductsLoading,
        isError: ordersProductsError,
    } = useGetOrderProductsSumQuery(dateRange, { refetchOnMountOrArgChange: true });

    const onDate = (startDate: Date, endDate: Date) => {
        setDateRange({
            startTime: DateUtils.getStartOfDayNumber(startDate),
            endTime: DateUtils.getEndOfDayNumber(endDate),
        });
    };

    return (
        <div className="flex flex-col gap-2">
            <AdminReportControllerComponent onDate={onDate} />
            <StatusComponent
                isEmpty={!ordersProducts?.length}
                isLoading={ordersProductsLoading}
                isError={ordersProductsError}
                height={isXs ? 7.6 : isSm ? 8 : 8.1}
            >
                <TableComponent
                    headers={AdminMostSellingProductsReportTableHeaders()}
                    items={ordersProducts || []}
                    selectors={(item: IMostSellingProductsModel) => [
                        item.name,
                        item.quantity,
                        item.discount.toFixed(2),
                        item.subTotal.toFixed(2),
                        ...(hasTobaccoTax ? [item.tobaccoTax.toFixed(2)] : []),
                        item.vat.toFixed(2),
                        item.total.toFixed(2),
                    ]}
                />
            </StatusComponent>
        </div>
    );
};

export default AdminMostSellingProductsPage;
