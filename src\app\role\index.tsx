import GuardedRouteComponent from "../../common/components/GuardedRouteComponent";
import OnlineStatusGuardComponent from "../../common/components/OnlineStatusGuardComponent";
import RandomBackGroundImageComponent from "../../common/components/RandomBackGroundImageComponent";
import { RoutsConstants } from "../../common/constants/RoutesConstants";
import { OrganizationHelper } from "../../common/helpers/OrganizationHelper";
import RoleEnterPasswordFeature from "./features/RoleEnterPasswordFeature";
import RoleTopSectionFeature from "./features/RoleTopSectionFeature";

const RolePage = () => {
    return (
        <OnlineStatusGuardComponent>
            <GuardedRouteComponent authGuard={true}>
                <GuardedRouteComponent
                    guard={!OrganizationHelper.hasRole()}
                    pathToRedirect={
                        OrganizationHelper.isAdmin()
                            ? RoutsConstants.admin.categories.fullPath
                            : RoutsConstants.pos.home.fullPath
                    }
                >
                    <RandomBackGroundImageComponent>
                        <div className="h-s flex flex-col justify-center items-center">
                            <RoleTopSectionFeature />
                            <RoleEnterPasswordFeature />
                        </div>
                    </RandomBackGroundImageComponent>
                </GuardedRouteComponent>
            </GuardedRouteComponent>
        </OnlineStatusGuardComponent>
    );
};

export default RolePage;
