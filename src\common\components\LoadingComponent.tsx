import { TranslateConstants } from "../constants/TranslateConstants";
import { loadingSelector } from "../redux/data/selector";
import { useAppSelector } from "../redux/store";
import logo from "/logo.svg";
import { useTranslate } from "../hooks/useTranslate";

const LoadingComponent = () => {
    const loading = useAppSelector(loadingSelector);
    const { translate } = useTranslate();

    if (!loading) return <></>;

    return (
        <div className="h-s w-s fixed z-50 top-0">
            <div className="bg-gray-800 opacity-75 h-s w-s"></div>
            <div className="text-center fixed top-0 bg- flex justify-center items-center h-s w-s flex-col gap-2">
                <img src={logo} alt="logo" className="animate-h_spin w-20 mb-2" />
                <h1
                    className={`text-xl text-white`}
                >
                    {translate(TranslateConstants.APP_NAME)}
                </h1>
            </div>
        </div>
    );
};

export default LoadingComponent;
