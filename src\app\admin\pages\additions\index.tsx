import useFetch from "../../../../common/asyncController/useFetch";
import useFlatMutate from "../../../../common/asyncController/useFlatMutate";
import AddAndFilterComponent from "../../../../common/components/AddAndFilterComponent";
import StatusComponent from "../../../../common/components/StatusComponent";
import TableComponent from "../../../../common/components/TableComponent";
import { EndPointsConstants } from "../../../../common/constants/ApiConstants";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import { IAdditionModel } from "../../../../common/models/AdditionsModel";
import useActions from "../../../../common/redux/data/useActions";
import { AdditionsApiRepo } from "../../../../common/repos/api/AdditionsApiRepo";
import { AdminAdditionDataTableHeaders } from "./AdminAdditionsConstants";
import AdminAdditionsModal from "./modals/AdminAdditionsModal";

const AdminAdditionsPage = () => {
    const actions = useActions();
    const { isSm } = useScreenSize();

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsConstants.ADDITIONS,
        AdditionsApiRepo.getAdditions
    );

    const updateAddition = useFlatMutate(AdditionsApiRepo.updateAddition, {
        updateCached: {
            key: EndPointsConstants.ADDITIONS,
            operation: "update",
            selector: (data: IAdditionModel) => data.id,
        },
    });

    const handleOnActive = (active: boolean, item: IAdditionModel) =>
        updateAddition(item.id, { active });

    const handleOnClick = (isEdit?: boolean, item?: IAdditionModel) => {
        actions.openModal({
            component: <AdminAdditionsModal item={item} />,
            title: isEdit ? TranslateConstants.EDIT : TranslateConstants.ADD,
            size: "md",
            showButtons: false,
        });
    };

    return (
        <>
            <AddAndFilterComponent onClick={handleOnClick} onReload={refetch} />
            <StatusComponent
                isLoading={isLoading}
                isError={isError}
                isEmpty={!data || data.length === 0}
                height={isSm ? 7.3 : 8}
            >
                <TableComponent
                    headers={AdminAdditionDataTableHeaders}
                    items={data || []}
                    selectors={(item: IAdditionModel) => [
                        item.number,
                        item.name,
                        item.price || "-",
                    ]}
                    showEditButton={true}
                    onEdit={(item: IAdditionModel) => handleOnClick(true, item)}
                    showActiveButton={true}
                    activeSelector={(item: IAdditionModel) => item.active}
                    onActive={handleOnActive}
                />
            </StatusComponent>
        </>
    );
};

export default AdminAdditionsPage;
