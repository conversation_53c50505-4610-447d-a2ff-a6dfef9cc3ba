import { TranslateConstants } from "../constants/TranslateConstants";
import { useTranslate } from "../hooks/useTranslate";

const ErrorComponent = () => {
    const { translate } = useTranslate();

    return (
        <div className="w-full h-full flex justify-center items-center text-red-500 font-bold">
            {translate(TranslateConstants.ERROR_PLEASE_TRY_AGAIN)}
        </div>
    );
}

export default ErrorComponent;
