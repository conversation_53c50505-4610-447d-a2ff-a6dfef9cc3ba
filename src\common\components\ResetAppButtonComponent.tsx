import { APP_LOCAL_DB_COLLECTIONS, AppLocalDB } from "../config/localDB";
import { TranslateConstants } from "../constants/TranslateConstants";
import { AppHelper } from "../helpers/AppHelper";
import { <PERSON><PERSON>Helper } from "../helpers/CookiesHelper";
import { LocalStorageHelper } from "../helpers/LocalStorageHelper";
import { SessionStorageHelper } from "../helpers/SessionStorageHelper";
import { ToastHelper } from "../helpers/ToastHelper";
import { TranslateHelper } from "../helpers/TranslateHelper";
import useActions from "../redux/data/useActions";
import { debug } from "../utils/CommonUtils";

const ResetAppButtonComponent = () => {
    const actions = useActions();

    const handleBackupData = async () => {
        try {
            actions.setLoading();
            await AppLocalDB.backupAndDownload([
                APP_LOCAL_DB_COLLECTIONS.ORDERS,
                APP_LOCAL_DB_COLLECTIONS.SHIFT,
            ]);
            ToastHelper.success(TranslateConstants.BACK_UP_SUCCESSFULLY);
        } catch (error) {
            debug("backup error", error);
            ToastHelper.error(TranslateConstants.BACK_UP_FAILED);
        } finally {
            actions.setLoading(false);
        }
    }

    const handleRestoreData = async (file: File | null) => {
        if (!file) return;

        try {
            actions.setLoading();
            await AppLocalDB.restoreFromFile(file);
            ToastHelper.success(TranslateConstants.BACK_UP_RESTORED_SUCCESSFULLY);
        } catch (err) {
            console.error("Restore failed:", err);
            ToastHelper.error(TranslateConstants.BACK_UP_RESTORE_FAILED);
        } finally {
            actions.setLoading(false);
        }
    }

    const triggerFileInput = () => {
        const fileInput = document.createElement("input");
        fileInput.type = "file";
        fileInput.accept = 'application/json';
        fileInput.onchange = (e: Event) => {
            const file = (e.target as HTMLInputElement).files?.[0];
            if (file) handleRestoreData(file);
        };
        fileInput.click();
    };

    const handleOnReset = async () => {
        CookiesHelper.clear();
        LocalStorageHelper.clear();
        SessionStorageHelper.clear();
        await AppLocalDB.clear();
        window.location.assign("/");
    };

    if (!AppHelper.isTestMode()) return <></>;
    return (
        <>
            <div className="fixed bottom-[44%] z-50 group bg-[#226bb2] border-l-2 border-gray-200 h-24 w-1 cursor-pointer rounded-l flex items-center font-tajawal-bold active:scale-95">
                <button
                    className="rounded hover:right-0 h-full bg-[#226bb2] text-white p-2 cursor-pointer hidden group-hover:block border border-r-0"
                    onClick={handleBackupData}
                >
                    {TranslateHelper.t(TranslateConstants.BACK_UP)}
                </button>
            </div>
            <div className="fixed bottom-[32%] z-50 group bg-[#226bb2] border-l-2 border-gray-200 h-24 w-1 cursor-pointer rounded-l flex items-center font-tajawal-bold active:scale-95">
                <button
                    className="rounded hover:right-0 h-full bg-[#226bb2] text-white p-2 cursor-pointer hidden group-hover:block border border-r-0"
                    onClick={triggerFileInput}
                >
                    {TranslateHelper.t(TranslateConstants.RESTORE_BACK_UP)}
                </button>
            </div>
            <div className="fixed bottom-[20%] z-50 group bg-red-700 border-l-2 border-gray-200 h-24 w-1 cursor-pointer rounded-l flex items-center font-tajawal-bold active:scale-95">
                <button
                    className="rounded hover:right-0 h-full bg-red-700 text-white p-2 cursor-pointer hidden group-hover:block border border-r-0"
                    onClick={handleOnReset}
                >
                    {TranslateHelper.t(TranslateConstants.RESET_APP)}
                </button>
            </div>
        </>
    );
};

export default ResetAppButtonComponent;
