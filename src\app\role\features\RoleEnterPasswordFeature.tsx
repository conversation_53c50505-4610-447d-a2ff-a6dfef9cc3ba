import { useState } from "react";
import ButtonComponent from "../../../common/components/ButtonComponent";
import NumpadComponent from "../../../common/components/NumpadComponent";
import { TranslateConstants } from "../../../common/constants/TranslateConstants";
import useFlatMutate from "../../../common/asyncController/useFlatMutate";
import { OrganizationRepo } from "../../admin/repos/OrganizationRepo";
import { ToastHelper } from "../../../common/helpers/ToastHelper";
import { OrganizationHelper } from "../../../common/helpers/OrganizationHelper";
import { RoutsConstants } from "../../../common/constants/RoutesConstants";
import { useNavigate } from "react-router-dom";
import { OrganizationRoleEnum } from "../../../common/enums/DataEnums";
import { isDesktop } from "react-device-detect";

const RoleEnterPasswordFeature = () => {
    const [password, setPassword] = useState("");
    const navigate = useNavigate();
    const organization = OrganizationHelper.getOrganization();

    const mutate = useFlatMutate(OrganizationRepo.checkRole, {
        onError: ({ error }) => {
            if (error === "INVALID_PASSWORD") {
                ToastHelper.error(TranslateConstants.INVALID_PASSWORD);
                setPassword("");
            }
        },
        onSuccess: ({ data }) => {
            setPassword("");
            if (data) {
                if (data === OrganizationRoleEnum.POS && !isDesktop) {
                    ToastHelper.warning(TranslateConstants.POS_IS_AVAILABLE_ON_DESKTOP_ONLY);
                    return;
                }

                OrganizationHelper.setRole(data);
                if (data === OrganizationRoleEnum.ADMIN)
                    navigate(RoutsConstants.admin.categories.fullPath);
                if (data === OrganizationRoleEnum.POS)
                    navigate(RoutsConstants.pos.home.fullPath);
            }
        },
    });

    return (
        <div className="flex justify-center items-center h-full w-full">
            <div className="flex flex-col gap-2 sm:gap-3 items-center">
                <div className="flex flex-col items-center gap-1">
                    {organization?.logo && (
                        <img
                            src={organization?.logo}
                            alt="logo"
                            className="w-12 h-12 sm:w-20 sm:h-20 rounded-full"
                        />
                    )}
                    <div className="flex flex-col items-center">
                        <span className="text-white sm:text-2xl font-tajawal-bold">
                            {organization?.name}
                        </span>
                        {organization?.subName && (
                            <span className="text-white text-lg font-tajawal-bold hidden sm:block">
                                {organization?.subName}
                            </span>
                        )}
                    </div>
                </div>
                <NumpadComponent
                    allowZeroAtFirst={true}
                    disableResultInput={false}
                    placeHolder="****"
                    isSecured={true}
                    allowDot={false}
                    padding="p-2"
                    onChange={(val) => setPassword(val)}
                    value={password}
                />
                <ButtonComponent
                    text={TranslateConstants.LOGIN}
                    className="!w-full py-5"
                    textSize="text-lg"
                    isDisabled={password.length < 4 || password.length > 8}
                    onClick={() => mutate(password)}
                />
            </div>
        </div>
    );
};

export default RoleEnterPasswordFeature;
