import { ProductSizeTypeEnum } from "../../../common/enums/DataEnums";
import { ITableModel } from "../../../common/models/TableModel";
import {
    IPosOrder,
    IPosOrderProduct,
    IPosOrderProductAddition,
} from "../interface";
import IState, { IPosHomeState } from "./interface";

export const setData = (
    state: IState,
    action: {
        payload: {
            type: "products" | "categories" | "tables" | "discounts" | "additions";
            data: any[];
        };
        type: string;
    }
) => {
    switch (action.payload.type) {
        case "products":
            return { ...state, products: action.payload.data };
        case "categories":
            return { ...state, categories: action.payload.data };
        case "tables":
            return { ...state, tables: action.payload.data };
        case "discounts":
            return { ...state, discounts: action.payload.data };
        case "additions":
            return { ...state, additions: action.payload.data };
        default:
            return state;
    }
};

export const updateTable = (
    state: IState,
    action: {
        payload: ITableModel;
    }
) => {
    const tables = state.tables;
    const index = tables.findIndex((t) => t.id === action.payload.id);
    if (index !== -1) tables[index] = action.payload;
};

export const setOrder = (
    state: IState,
    action: { payload: Partial<IPosOrder>; type: string }
) => {
    return { ...state, order: { ...state.order, ...action.payload } };
};

export const setOrderProduct = (
    state: IState,
    action: {
        payload: {
            productIndex?: number;
            product: IPosOrderProduct;
            isQuantityChange: boolean
        };
        type: string;
    }
) => {
    const products = state.order.products;
    const product = action.payload.product;
    const productIndex = action.payload.productIndex;
    const isFixed = product.productSizeType === ProductSizeTypeEnum.FIXED;
    const index = products.findIndex((p) => {
        if (isFixed) return p.product.id === product.product.id && !p.product.isIncludingAdditions;
        else return p.product.id === product.product.id && p.size?.id === product.size?.id && !p.product.isIncludingAdditions;
    });

    if (index === -1 && productIndex === undefined) products.push(product);
    else if (index !== -1 && productIndex === undefined) {
        ++products[index].quantity;
    } else if (productIndex !== undefined) {
        if (product.quantity <= 0) products.splice(productIndex, 1);
        else if (action.payload.isQuantityChange) products[productIndex].quantity = product.quantity;
    }
};

export const setOrderProductAddition = (
    state: IState,
    action: {
        payload: {
            productIndex: number;
            addition: IPosOrderProductAddition;
            isQuantityChange: boolean;
        };
        type: string;
    }
) => {
    const currentAdditions = state.order.products[action.payload.productIndex].additions || [];
    const additionIndex = currentAdditions.findIndex((a) => a.addition.id === action.payload.addition.addition.id)
    if (additionIndex !== -1) {
        if (action.payload.addition.quantity <= 0)
            currentAdditions.splice(additionIndex, 1);
        else if (action.payload.isQuantityChange)
            currentAdditions[additionIndex].quantity = action.payload.addition.quantity
        else
            currentAdditions[additionIndex].quantity += 1;
    } else currentAdditions.push(action.payload.addition);

    state.order.products[action.payload.productIndex].additions = currentAdditions;
};

export const setHome = (
    state: IState,
    action: {
        payload: Partial<IPosHomeState>;
        type: string;
    }
) => {
    state.home = { ...state.home, ...action.payload };
    return state;
};
