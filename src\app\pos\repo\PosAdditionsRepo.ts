import {
    APP_LOCAL_DB_COLLECTIONS,
    AppLocalDB,
} from "../../../common/config/localDB";
import { EndPointsConstants } from "../../../common/constants/ApiConstants";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { IAdditionModel } from "../../../common/models/AdditionsModel";
import { debug } from "../../../common/utils/CommonUtils";

export class PosAdditionsRepo {
    static async getAndCacheAdditions(updatedAt?: Date) {
        try {
            const res = await AxiosHelper.get<IAdditionModel[]>(
                EndPointsConstants.ADDITIONS,
                { params: { updatedAt, deliveryApps: true } }
            );

            if (!res.success) throw new Error(res.message);
            const data = res.data || [];

            if (!updatedAt) await AppLocalDB.set(APP_LOCAL_DB_COLLECTIONS.ADDITIONS, data);
            else await AppLocalDB.add(APP_LOCAL_DB_COLLECTIONS.ADDITIONS, data, true);
        } catch (error) {
            debug(`PosAdditionsRepo [getAndCacheAdditions] Error: `, error);
            throw error;
        }
    }

    static async getAdditions(activeOnly: boolean = false) {
        try {
            const res = await AppLocalDB.get<IAdditionModel>(
                APP_LOCAL_DB_COLLECTIONS.ADDITIONS,
                { where: activeOnly ? [[["active", "==", true]]] : undefined }
            );

            if (!res) {
                throw new Error("No additions found");
            }

            return { data: res };
        } catch (error) {
            debug(`PosAdditionsRepo [getAdditions] Error: `, error);
            throw error;
        }
    }
}
