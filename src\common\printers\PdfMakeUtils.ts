import pdfMake from "pdfmake/build/pdfmake";
import { Content } from 'pdfmake/interfaces';
import { Tajawal_Regular_Font_Base64 } from "../assets/base64/Tajawal-Regular-Font-Base64";
import { Tajawal_Bold_Font_Base64 } from "../assets/base64/Tajawal-Bold-Font-Base64";
import { DateUtils } from "../utils/DateUtils";
import { PdfMakeHelper } from "./PdfMakeHelper";
import { debug } from "../utils/CommonUtils";
import { PdfMakeProps } from "./PdfMakeInterfaces";

pdfMake.vfs = {
    "Tajawal-Regular.ttf": Tajawal_Regular_Font_Base64,
    "Tajawal-Bold.ttf": Tajawal_Bold_Font_Base64,
};

pdfMake.fonts = {
    Tajawal: {
        normal: "Tajawal-Regular.ttf",
        bold: "Tajawal-Bold.ttf",
    },
};

pdfMake.tableLayouts = {
    customTableLayout: {
        hLineWidth: () => 1,
        vLineWidth: () => 1,
        hLineColor: () => '#000000',
        vLineColor: () => '#000000',
        paddingLeft: () => 5,
        paddingRight: () => 5,
        paddingTop: () => 4,
        paddingBottom: () => 4,
    },
};

export class PdfMakeUtils {
    static download = async (content: Content, fileName: string, options?: PdfMakeProps) => {
        try {
            pdfMake.createPdf(await PdfMakeHelper.defaultProps(content, false, options))
                .download(fileName + " - " + DateUtils.format(Date.now(), "yyyy-MM-dd hh-mm A", true) + ".pdf");
        } catch (error) {
            debug(`PdfMakeUtils [download] Error: ${error}`);
        }
    }

    static preview = async (content: Content, options?: PdfMakeProps) => {
        try {
            pdfMake.createPdf(await PdfMakeHelper.defaultProps(content, false, options)).open();
        } catch (error) {
            debug(`PdfMakeUtils [preview] Error: ${error}`);
        }
    }

    static print = async (content: Content, options?: PdfMakeProps) => {
        try {
            pdfMake.createPdf(await PdfMakeHelper.defaultProps(content, true, options)).getBlob((blob: Blob) => {
                const blobUrl = URL.createObjectURL(blob);

                document.getElementById("pdf-iframe")?.remove();

                // Create invisible iframe
                const iframe = document.createElement("iframe");
                iframe.style.display = "none";
                iframe.src = blobUrl;
                iframe.id = "pdf-iframe";

                document.body.appendChild(iframe);

                iframe.onload = () => {
                    iframe.contentWindow?.focus();
                    iframe.contentWindow?.print();
                };

                // Cleanup
                setTimeout(() => {
                    URL.revokeObjectURL(blobUrl);
                    iframe.remove();
                }, 1000 * 30);
            });
        } catch (error) {
            debug(`PdfMakeUtils [print] Error: ${error}`);
        }
    }

}