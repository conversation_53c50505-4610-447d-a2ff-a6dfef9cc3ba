import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../../common/helpers/ToastHelper";
import { IAdminCategoryInputs } from "./AdminCategoriesInterface";

export class AdminCategoriesValidation {
    static inputsValidation = (values: IAdminCategoryInputs) => {
        let isValid = true;

        if (!values.name) {
            isValid = false;
            ToastHelper.error(TranslateConstants.NAME_FIELD_IS_REQUIRED);
        }

        return isValid;
    };
}
