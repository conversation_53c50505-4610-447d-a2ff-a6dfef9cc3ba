import { IMultiInputsItem } from "../../../../common/components/MultiInputsComponent";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { ProductSizeTypeEnum } from "../../../../common/enums/DataEnums";
import { IAdminProductInputs } from "./AdminProductsInterface";

export const AdminProductSizeInputs: IMultiInputsItem[] = [
    { name: "name" },
    { name: "price", placeholder: "0.00" },
];

export const AdminProductInputs: IAdminProductInputs = {
    name: "",
    price: undefined,
    image: null,
    categoryId: undefined,
    sizes: [],
    isIncludingAdditions: false,
    productSizeType: ProductSizeTypeEnum.FIXED,
    isSubjectToTobaccoTax: false,
    active: true,
};

export const AdminProductDataTableHeaders = [
    TranslateConstants.NUMBER,
    TranslateConstants.NAME,
    TranslateConstants.CATEGORY,
    TranslateConstants.PRODUCT_TYPE,
    TranslateConstants.PRICE,
];

export const AdminProductSizeTypeOptions = [
    {
        value: ProductSizeTypeEnum.FIXED,
        label: TranslateConstants.FIXED_SIZE,
    },
    {
        value: ProductSizeTypeEnum.MULTIPLE,
        label: TranslateConstants.MULTIPLE_SIZE,
    },
];

export const AdminProductSizeHeaders = [
    TranslateConstants.NAME,
    TranslateConstants.PRICE,
]