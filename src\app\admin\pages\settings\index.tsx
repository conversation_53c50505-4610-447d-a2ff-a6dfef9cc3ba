import { useMemo, useState } from "react";
import { AdminSettingsConstants } from "./AdminSettingsConstants";
import { AdminSettingsValidation } from "./AdminSettingsValidation";
import { AdminSettingsService } from "./AdminSettingsService";
import { OrganizationHelper } from "../../../../common/helpers/OrganizationHelper";
import useScreenSize from "../../../../common/hooks/useScreenSize";
import useActions from "../../../../common/redux/data/useActions";
import ListComponent from "../../../../common/components/ListComponent";
import InputComponent from "../../../../common/components/InputComponent";
import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import UploadFileComponent from "../../../../common/components/UploadImageComponent";
import ButtonComponent from "../../../../common/components/ButtonComponent";
import StatusComponent from "../../../../common/components/StatusComponent";
import useFetch from "../../../../common/asyncController/useFetch";
import { OrganizationRepo } from "../../repos/OrganizationRepo";
import { EndPointsConstants } from "../../../../common/constants/ApiConstants";
import CheckBoxComponent from "../../../../common/components/CheckBoxComponent";

const AdminSettingsPage = () => {
    const organization = OrganizationHelper.getOrganization();
    const { isXs, isSm } = useScreenSize();
    const actions = useActions();
    const [state, setState] = useState(AdminSettingsConstants());

    const { data, isLoading, isError, refetch } = useFetch(
        EndPointsConstants.ORGANIZATIONS_OWN_SENSITIVES,
        OrganizationRepo.getSensitives,
        {
            onData: (data) => setState(AdminSettingsConstants(data)),
        }
    );

    const handleOnClick = () =>
        AdminSettingsService.updateOrganization(state, actions, setState, refetch);
    const isDeeplyEqual = useMemo(
        () => AdminSettingsValidation.checkDeeplyEqual(state, data),
        [state, data]
    );

    return (
        <StatusComponent
            height={isSm ? 4.5 : 5}
            isLoading={isLoading}
            isError={isError}
        >
            <ListComponent cols={isXs ? 1 : 2} padding="0">
                <InputComponent
                    label={TranslateConstants.ORGANIZATION_NAME_AR}
                    value={state.name}
                    onChange={(name) => setState({ ...state, name })}
                />
                <InputComponent
                    label={TranslateConstants.ORGANIZATION_NAME_EN}
                    value={state.nameEn}
                    onChange={(nameEn) => setState({ ...state, nameEn })}
                />
                <InputComponent
                    label={TranslateConstants.SUB_NAME_AR}
                    value={state.subName}
                    onChange={(subName) => setState({ ...state, subName })}
                />
                <InputComponent
                    label={TranslateConstants.SUB_NAME_EN}
                    value={state.subNameEn}
                    onChange={(subNameEn) => setState({ ...state, subNameEn })}
                />
                <InputComponent
                    type="password"
                    label={TranslateConstants.PASSWORD}
                    placeholder="********"
                    value={state.password}
                    onChange={(password) => setState({ ...state, password })}
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.TAX_NUMBER}
                    value={state.taxNumber}
                    onChange={(taxNumber) => setState({ ...state, taxNumber })}
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.REGISTRATION_NUMBER}
                    value={state.registrationNumber}
                    onChange={(registrationNumber) =>
                        setState({ ...state, registrationNumber })
                    }
                />
                <InputComponent
                    type="number"
                    label={TranslateConstants.MOBILE}
                    value={state.mobile}
                    onChange={(mobile) => setState({ ...state, mobile })}
                />
                <InputComponent
                    label={TranslateConstants.ADDRESS}
                    value={state.address}
                    onChange={(address) => setState({ ...state, address })}
                />
                <InputComponent
                    type="password"
                    label={TranslateConstants.ADMIN_PASSWORD}
                    placeholder="********"
                    value={state.adminPassword}
                    onChange={(adminPassword) => setState({ ...state, adminPassword })}
                />
                <InputComponent
                    type="password"
                    label={TranslateConstants.POS_PASSWORD}
                    placeholder="********"
                    value={state.posPassword}
                    onChange={(posPassword) => setState({ ...state, posPassword })}
                />
                <InputComponent
                    label={TranslateConstants.INVOICE_FOOTER}
                    value={state.invoiceFooter}
                    onChange={(invoiceFooter) => setState({ ...state, invoiceFooter })}
                />
                <div className="flex justify-start items-center h-[8.7rem] gap-5 md:gap-0 sm:col-span-2">
                    <UploadFileComponent
                        label={TranslateConstants.LOGO}
                        image={organization?.logo}
                        onFileChange={(logo) => setState({ ...state, logo })}
                        containerClassName="sm:!w-1/4"
                    />
                </div>
                <div className="flex justify-start items-center my-4 md:gap-0 sm:col-span-2">
                    <CheckBoxComponent
                        onChange={(val) => setState({ ...state, activateTobaccoTax: val })}
                        label={TranslateConstants.ACTIVATE_TOBACCO_TAX}
                        value={state.activateTobaccoTax}
                    />
                </div>
                <div className="flex flex-col gap-4">
                    <ButtonComponent
                        text={TranslateConstants.SAVE}
                        className="md:!w-1/2"
                        onClick={handleOnClick}
                        isDisabled={isDeeplyEqual}
                    />
                </div>
            </ListComponent>
        </StatusComponent>
    );
};

export default AdminSettingsPage;
