import { TranslateConstants } from "../../../../common/constants/TranslateConstants";
import { DeliveryAppEnum } from "../../../../common/enums/DataEnums";
import { ShiftHelper } from "../../../../common/helpers/ShiftHelper";
import { TranslateHelper } from "../../../../common/helpers/TranslateHelper";
import { IPosShiftModel } from "../../../../common/models/PosShiftModel";
import { IShiftPrinterBodyModel } from "../../../../common/models/ShiftPrinterModel";
import { PosOrderRepo } from "../../repo/PosOrderRepo";

export class PosHomeUtils {
    static async getCurrentShiftData(endAmount: number): Promise<IPosShiftModel> {
        const shift = ShiftHelper.get();
        if (!shift) {
            throw new Error("Shift not found");
        }

        let totalAmount = 0
        let cashAmount = 0
        let networkAmount = 0
        let vatAmount = 0
        let discountAmount = 0
        let tobaccoTaxAmount = 0
        let deliveryAppsAmount = 0

        const { count: ordersCount, data: orders } =
            await PosOrderRepo.getAndCountOrders();

        orders.forEach((el) => {
            totalAmount += el.total
            cashAmount += el.cash
            networkAmount += el.network
            vatAmount += el.vat
            discountAmount += el.discount
            tobaccoTaxAmount += el.tobaccoTax
            if (el.deliveryApp) deliveryAppsAmount += el.total
        });

        const diffAmount = endAmount - (shift.startAmount + cashAmount)
        const additionAmount = diffAmount > 0 ? diffAmount : 0
        const shortageAmount = diffAmount < 0 ? Math.abs(diffAmount) : 0

        return {
            ...shift,
            endAmount,
            ordersCount,
            endTime: new Date(),
            totalAmount,
            discountAmount,
            tobaccoTaxAmount,
            cashAmount,
            networkAmount,
            vatAmount,
            additionAmount,
            shortageAmount,
            deliveryAppsAmount,
        };
    }

    static async getCurrentSiftDataWithDeliveryApps(endAmount: number): Promise<IShiftPrinterBodyModel> {
        const shift = await this.getCurrentShiftData(endAmount);
        const { data: orders } = await PosOrderRepo.getAndCountOrders();

        let hungerStation = 0;
        let jahez = 0;
        let toYou = 0;
        let theChefz = 0;
        let keeta = 0;
        let ninja = 0;
        let meshwarFood = 0;
        let locate = 0;

        orders.forEach((el) => {
            switch (el.deliveryApp) {
                case DeliveryAppEnum.HUNGER_STATION:
                    hungerStation += el.total;
                    break;
                case DeliveryAppEnum.JAEHEZ:
                    jahez += el.total;
                    break;
                case DeliveryAppEnum.TO_YOU:
                    toYou += el.total;
                    break;
                case DeliveryAppEnum.THE_CHEFZ:
                    theChefz += el.total;
                    break;
                case DeliveryAppEnum.KEETA:
                    keeta += el.total;
                    break;
                case DeliveryAppEnum.NINJA:
                    ninja += el.total;
                    break;
                case DeliveryAppEnum.MESHWAR_FOOD:
                    meshwarFood += el.total;
                    break;
                case DeliveryAppEnum.LOCATE:
                    locate += el.total;
                    break;
            }
        });

        const paymentAmounts = [
            { name: TranslateHelper.t(TranslateConstants.HUNGER_STATION), amount: hungerStation, nameEn: "Hunger Station" },
            { name: TranslateHelper.t(TranslateConstants.JAEHEZ), amount: jahez, nameEn: "Jahez" },
            { name: TranslateHelper.t(TranslateConstants.TO_YOU), amount: toYou, nameEn: "To You" },
            { name: TranslateHelper.t(TranslateConstants.THE_CHEFZ), amount: theChefz, nameEn: "The Chefz" },
            { name: TranslateHelper.t(TranslateConstants.KEETA), amount: keeta, nameEn: "Keeta" },
            { name: TranslateHelper.t(TranslateConstants.NINJA), amount: ninja, nameEn: "Ninja" },
            { name: TranslateHelper.t(TranslateConstants.MESHWAR_FOOD), amount: meshwarFood, nameEn: "Meshwar Food" },
            { name: TranslateHelper.t(TranslateConstants.LOCATE), amount: locate, nameEn: "Locate" },
        ];

        return {
            ...shift,
            paymentAmounts,
        };
    }
}