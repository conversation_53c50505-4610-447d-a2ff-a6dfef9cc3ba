import { FC, useMemo } from "react";
import PosOrderProductItem from "./PosOrderProductItem";
import EmptyComponent from "../../../../../common/components/EmptyComponent";
import ListComponent from "../../../../../common/components/ListComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { useTranslate } from "../../../../../common/hooks/useTranslate";
import { IPosOrder } from "../../../interface";
import { OrderTypeEnum } from "../../../../../common/enums/DataEnums";
import { OrganizationHelper } from "../../../../../common/helpers/OrganizationHelper";

interface IProps {
    order: IPosOrder;
}

const PosOrderProductComponent: FC<IProps> = ({ order }) => {
    const { translate } = useTranslate();
    const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();

    const calcHeight = useMemo(() => {
        let height = 20.5;
        if (order.type !== OrderTypeEnum.TAKE_AWAY) height += 1.8;
        if (order.customer) height += 1.8;
        if (hasTobaccoTax) height += 1.8;
        return height;
    }, [order.type, order.customer]);

    if (order.products.length === 0) return <EmptyComponent />;

    return (
        <>
            <div className="flex justify-between p-2">
                <p>
                    <span>{translate(TranslateConstants.PRODUCTS)}</span>
                    <span>({order.products.length})</span>
                </p>
                <p>{translate(TranslateConstants.QUANTITY)}</p>
            </div>
            <ListComponent
                padding="0"
                allowScrollBar={false}
                calcHeight={calcHeight}
                space=".4"
                height=""
            >
                {order.products.map((el, index) => (
                    <PosOrderProductItem
                        key={index}
                        productIndex={index}
                        product={el}
                        orderStatus={order.status}
                        productsCount={order.products.length}
                    />
                ))}
            </ListComponent>
        </>
    );
};

export default PosOrderProductComponent;
