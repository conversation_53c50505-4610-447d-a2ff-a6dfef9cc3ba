import { TranslateConstants } from "../../../common/constants/TranslateConstants";
import { ToastHelper } from "../../../common/helpers/ToastHelper";
import { debug } from "../../../common/utils/CommonUtils";
import { PosCategoriesRepo } from "../repo/PosCategoriesRepo";
import { Dispatch, SetStateAction } from "react";
import { PosProductsRepo } from "../repo/PosProductsRepo";
import { PosTablesRepo } from "../repo/PosTablesRepo";
import { LocalStorageHelper } from "../../../common/helpers/LocalStorageHelper";
import { currentAppVersionKeyConstant, isPosInitKeyConstant } from "../../../common/constants/ConfigConstants";
import { IPosActions } from "../redux/usePosActions";
import { IActions } from "../../../common/redux/data/useActions";
import { ShiftHelper } from "../../../common/helpers/ShiftHelper";
import { PosSettingsService } from "../pages/settings/PosSettingsService";
import { OrganizationHelper } from "../../../common/helpers/OrganizationHelper";
import { PosOrderRepo } from "../repo/PosOrderRepo";
import { OrderHelper } from "../../../common/helpers/OrderHelper";
import { PosDiscountsRepo } from "../repo/PosDiscountsRepo";
import { AppVersion } from "../../../common/constants/CommonConstants";
import { PosCustomersRepo } from "../repo/PosCustomersRepo";
import { PosAdditionsRepo } from "../repo/PosAdditionsRepo";
import { PosDeliveryAppsRepo } from "../repo/PosDeliveryAppsRepo";

export class PosLayoutService {
    static async checkAppVersion() {
        const currentAppVersion = LocalStorageHelper.get(currentAppVersionKeyConstant);
        if (!currentAppVersion || currentAppVersion !== AppVersion) {
            // set action for not match version
            // LocalStorageHelper.remove(isPosInitKeyConstant); // reset local db data and then init Line:31
        }
        LocalStorageHelper.set(currentAppVersionKeyConstant, AppVersion);
    }

    static async init(
        setLoading: Dispatch<SetStateAction<{ progress: number; loading: boolean; }>>,
        actions: IActions
    ) {
        if (LocalStorageHelper.get(isPosInitKeyConstant)) return;

        try {
            setLoading({ progress: 0, loading: true });
            await PosCategoriesRepo.getAndCacheCategories();
            setLoading((prev) => ({ ...prev, progress: 10 }));
            await PosProductsRepo.getAndCacheProducts();
            setLoading((prev) => ({ ...prev, progress: 40 }));
            await PosTablesRepo.getAndCacheTables();
            setLoading((prev) => ({ ...prev, progress: 60 }));
            await PosDiscountsRepo.getAndCacheDiscounts();
            setLoading((prev) => ({ ...prev, progress: 70 }));
            await PosCustomersRepo.getAndCacheCustomers();
            setLoading((prev) => ({ ...prev, progress: 80 }));
            await PosAdditionsRepo.getAndCacheAdditions();
            setLoading((prev) => ({ ...prev, progress: 90 }));
            await PosDeliveryAppsRepo.getAndCacheDeliveryApps();
            setLoading((prev) => ({ ...prev, progress: 95 }));
            const ordersCount = await PosOrderRepo.getOrdersCount();
            OrderHelper.setInvoiceNumber(ordersCount);

            const updatesCount = await PosSettingsService.checkUpdates(actions);
            if (updatesCount) OrganizationHelper.setUpdatesMetaData(updatesCount);

            LocalStorageHelper.set(isPosInitKeyConstant, true);
        } catch (error) {
            debug(`PosInitService [init] Error: `, error);
            ToastHelper.error(TranslateConstants.POS_INIT_ERROR);
        } finally {
            setLoading({ progress: 100, loading: false });
        }
    }

    static async getAllData(posActions: IPosActions, actions: IActions) {
        try {
            actions.setLoading();
            const categories = await PosCategoriesRepo.getCategories(true);
            posActions.setCategories(categories.data);
            const products = await PosProductsRepo.getProducts(true);
            posActions.setProducts(products.data);
            const tables = await PosTablesRepo.getTables(true);
            posActions.setTables(tables.data);
            const discounts = await PosDiscountsRepo.getDiscounts(true);
            posActions.setDiscounts(discounts.data);
            const additions = await PosAdditionsRepo.getAdditions(true);
            posActions.setAdditions(additions.data);
            const isShiftOpen = ShiftHelper.isOpen();
            posActions.setHome({ isShiftOpen });
        } catch (error) {
            debug(`PosInitService [getAllData] Error: `, error);
            ToastHelper.error(TranslateConstants.POS_INIT_ERROR);
        } finally {
            actions.setLoading(false);
        }
    }
}
