import { IAdminDiscountInputs } from "../../../app/admin/pages/discounts/AdminDiscountsInterface";
import { EndPointsConstants } from "../../constants/ApiConstants";
import { AxiosHelper } from "../../helpers/AxiosHelper";
import { IDiscountModel } from "../../models/DiscountModel";
import { debug } from "../../utils/CommonUtils";

export class DiscountsApiRepo {
    static async getDiscounts() {
        try {
            const res = await AxiosHelper.get<IDiscountModel[]>(EndPointsConstants.DISCOUNTS);
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`DiscountsApiRepo [getDiscounts] Error: `, error);
            throw error;
        }
    }

    static async addDiscount(body: IAdminDiscountInputs) {
        try {
            const res = await AxiosHelper.post<IDiscountModel>(EndPointsConstants.DISCOUNTS, body);
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`DiscountsApiRepo [addDiscount] Error: `, error);
            throw error;
        }
    }

    static async updateDiscount(id: number, body: IAdminDiscountInputs) {
        try {
            const res = await AxiosHelper.patch<IDiscountModel>(EndPointsConstants.DISCOUNTS, id, body);
            if (!res.success || !res.data) throw new Error(res.message);
            return res.data;
        } catch (error) {
            debug(`DiscountsApiRepo [updateDiscount] Error: `, error);
            throw error;
        }
    }
}