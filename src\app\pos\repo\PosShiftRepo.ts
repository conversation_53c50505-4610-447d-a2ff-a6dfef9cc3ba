import {
    APP_LOCAL_DB_COLLECTIONS,
    AppLocalDB,
} from "../../../common/config/localDB";
import { EndPointsConstants } from "../../../common/constants/ApiConstants";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { IShiftBody } from "../../../common/interfaces/shift";
import { ILocalDBGetOptionsProperties } from "../../../common/LocalDB/LocalDBInterface";
import { IPosShiftModel } from "../../../common/models/PosShiftModel";
import { debug } from "../../../common/utils/CommonUtils";

export class PosShiftRepo {
    static async startShift(body: IPosShiftModel) {
        try {
            await AppLocalDB.add(APP_LOCAL_DB_COLLECTIONS.SHIFT, body);
        } catch (error) {
            debug(`PosShiftRepo [startShift] Error: `, error);
            throw error;
        }
    }

    static async endShift(body: IPosShiftModel) {
        try {
            await AppLocalDB.update(APP_LOCAL_DB_COLLECTIONS.SHIFT, body.id, body);
        } catch (error) {
            debug(`PosShiftRepo [endShift] Error: `, error);
            throw error;
        }
    }

    static async getShifts(options?: ILocalDBGetOptionsProperties<IPosShiftModel>) {
        try {
            return await AppLocalDB.get<IPosShiftModel>(APP_LOCAL_DB_COLLECTIONS.SHIFT, options);
        } catch (error) {
            debug(`PosShiftRepo [getShifts] Error: `, error);
            throw error;
        }
    }

    static async deleteAllShifts() {
        try {
            await AppLocalDB.deleteAll(APP_LOCAL_DB_COLLECTIONS.SHIFT);
        } catch (error) {
            debug(`PosShiftRepo [deleteAllShifts] Error: `, error);
            throw error;
        }
    }

    static async uploadShifts(body: IShiftBody[]) {
        try {
            const res = await AxiosHelper.post(EndPointsConstants.SHIFTS, body);
            if (!res.success) throw new Error(res.message);
        } catch (error) {
            debug(`PosShiftRepo [uploadShifts] Error: `, error);
            throw error;
        }
    }

    static async shiftsCount() {
        try {
            return await AppLocalDB.count(APP_LOCAL_DB_COLLECTIONS.SHIFT);
        }
        catch (error) {
            debug(`PosShiftRepo [shiftsCount] Error: `, error);
            throw error;
        }
    }
}
