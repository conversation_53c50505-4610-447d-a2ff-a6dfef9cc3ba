import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FetchState, UseFetchOptions } from "./interface";
import { AppDispatch, RootState } from "../redux/store";
import { fetchData, resetData } from "./slice";
import { setLoadingAction } from "../redux/data/slice";
import { ToastHelper } from "../helpers/ToastHelper";
import { debug } from "../utils/CommonUtils";
import { TranslateHelper } from "../helpers/TranslateHelper";
import { TranslateConstants } from "../constants/TranslateConstants";
import useOnlineStatus from "../hooks/useOnlineStatus";

const useFetch = <T>(
    key: string,
    promiseFn: (...val: any) => Promise<T>,
    options: UseFetchOptions<T> = {}
) => {
    const {
        autoFetchOnMount = false,
        autoFetchIfEmpty = true,
        resetOnUnmount = false,
        isShowLoadingPage = false,
        onSuccess,
        onFirstSuccess,
        onError,
        beforeStart,
        afterEnd,
        onData,
        onErrorToast,
        onSuccessToast,
        showDefaultErrorToast = false,
        isOnline = false,
    } = options;

    const initData: FetchState<T> = {
        data: undefined,
        isLoading: false,
        error: undefined,
        isError: false,
    };

    const dispatch = useDispatch<AppDispatch>();
    const onlineState = useOnlineStatus();
    const state: FetchState<T> = useSelector(
        (state: RootState) => state.fetch[key] || initData
    );

    useEffect(() => {
        if (onData) onData(state.data);
    }, [state.data]);

    useEffect(() => {
        if (autoFetchOnMount || (autoFetchIfEmpty && !state.data)) {
            handleFetch();
        }

        return () => {
            if (resetOnUnmount) dispatch(resetData(key));
        };
    }, [dispatch, key, autoFetchOnMount, resetOnUnmount, autoFetchIfEmpty]);

    const handleFetch = async (...param: any) => {
        if (isOnline && !onlineState) {
            ToastHelper.error(TranslateHelper.t(TranslateConstants.ERROR_NO_INTERNET_CONNECTION));
            return;
        }

        if (state.isLoading) return;
        let isFirstSuccess = !state.data;

        try {
            if (isShowLoadingPage) dispatch(setLoadingAction(true));
            if (beforeStart) beforeStart({ dispatch });

            const action = await dispatch(fetchData({ key, promiseFn: () => promiseFn(...param) }));
            if (fetchData.fulfilled.match(action)) {
                const payload = param.length === 1 ? param[0] : param;
                onSuccess?.({ data: action.payload.data, dispatch, payload });
                if (isFirstSuccess && onFirstSuccess) onFirstSuccess({ data: action.payload.data, dispatch, payload });
                if (onSuccessToast)
                    ToastHelper.success(TranslateHelper.t(onSuccessToast));
            }
            if (fetchData.rejected.match(action)) {
                onError?.({ error: (action.payload as any).error, dispatch });
                debug(
                    `Error in useFetch for key [${key}]: `,
                    (action.payload as any).error
                );
                if (onErrorToast) ToastHelper.error(TranslateHelper.t(onErrorToast));
            }
        } catch (err: any) {
            if (onErrorToast) ToastHelper.error(TranslateHelper.t(onErrorToast));
            else if (showDefaultErrorToast)
                ToastHelper.error(TranslateHelper.t(TranslateConstants.ERROR_PLEASE_TRY_AGAIN));

            onError?.({ error: err.message, dispatch });
            debug(`Error in useFetch for key [${key}]: `, err.message);
        } finally {
            if (isShowLoadingPage) dispatch(setLoadingAction(false));
            if (afterEnd) afterEnd({ dispatch });
        }
    };

    return { ...state, refetch: handleFetch };
};

export default useFetch;
