import { posPrintersKeyConstant } from "../constants/ConfigConstants";
import { PrinterEnum } from "../enums/DataEnums";
import { IPrinterData } from "../interfaces";
import { LocalStorageHelper } from "./LocalStorageHelper";

export class PrintersHelper {
    static getPrinters(): IPrinterData[] {
        const printers: IPrinterData[] = LocalStorageHelper.get(
            posPrintersKeyConstant
        );
        return printers || [];
    }

    static getDefaultPrinter(): IPrinterData | undefined {
        const printers = this.getPrinters();
        return printers.find((item) => item.categoryId === PrinterEnum.RECEIPT)
    };

    static setPrinters(printers: IPrinterData[]): void {
        LocalStorageHelper.set(posPrintersKeyConstant, printers);
    }
}
