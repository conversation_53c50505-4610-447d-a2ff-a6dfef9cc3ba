import { useEffect, useState } from "react";
import { TranslateHelper } from "../helpers/TranslateHelper";
import InputComponent from "./InputComponent";
import DividerComponent from "./DividerComponent";
import ButtonComponent from "./ButtonComponent";
import { TranslateConstants } from "../constants/TranslateConstants";
import { LuTrash2 } from "react-icons/lu";
import IconButtonComponent from "./IconButtonComponent";

export interface IMultiInputsItem {
    name: string;
    placeholder?: string;
}

export interface IDefaultItem<T> extends IMultiInputsItem {
    value: string;
    item?: T;
}

interface IProps<T> {
    headers?: string[];
    inputs?: IMultiInputsItem[];
    maxItems?: number;
    buttonText?: string;
    onResult?: (value: T[]) => void;
    defaultValues?: T[];
}

function MultiInputsComponent<T>({
    headers,
    inputs = [],
    maxItems = 10,
    buttonText,
    onResult,
    defaultValues = [],
}: IProps<T>) {
    const defaultItem: IDefaultItem<T>[] = inputs.map((item) => ({ ...item, value: "" }));
    const [state, setState] = useState<IDefaultItem<T>[][]>([defaultItem]);

    useEffect(() => {
        if (defaultValues.length > 0) {
            const newState = defaultValues.map((item) => {
                const obj: IDefaultItem<T>[] = inputs.map((input) => {
                    const value = item[input.name as keyof T] ?? "";
                    return { ...input, value: value as string, item };
                });
                return obj;
            });
            setState(newState);
            handleOnResult(newState);
        }
    }, []);

    const handleOnResult = (res: IDefaultItem<T>[][]) => {
        const result = res.map((item) => {
            const obj: any = {};
            item.forEach((input) => {
                obj[input.name] = input.value;
                if (input.item) obj.item = input.item;
            });
            return obj as T;
        });
        onResult?.(result);
    }

    const handleOninputChange = (
        value: string,
        containerIndex: number,
        inputIndex: number
    ) => {
        const newState = [...state];
        newState[containerIndex][inputIndex].value = value;
        setState(newState);
        handleOnResult(newState);
    }

    const handleOnRemove = (index: number) => {
        if (state.length <= 1) return;
        const newState = [...state];
        newState.splice(index, 1);
        setState(newState);
        handleOnResult(newState);
    }

    const handleOnAdd = () => {
        if (state.length >= maxItems) return;
        setState((prev) => [...prev, defaultItem]);
    }

    return (
        <>
            {!!headers && (
                <div className="w-full flex gap-2 p-1 bg-base-300 rounded-sm text-black dark:text-white font-bold">
                    {headers.map((header, index) => (
                        <span className="flex-1" key={index}>
                            {TranslateHelper.t(header)}
                        </span>
                    ))}
                    <span className="w-1/6"></span>
                </div>
            )}
            {state?.map((input, containerIndex) => (
                <div key={containerIndex}>
                    <div className="w-full flex gap-2" key={containerIndex}>
                        {input.map((item, inputIndex) => (
                            <InputComponent
                                key={inputIndex}
                                containerClassName="flex-1"
                                onChange={(value) => handleOninputChange(value, containerIndex, inputIndex)}
                                placeholder={item.placeholder}
                                value={item.value || ""}
                            />
                        ))}
                        <IconButtonComponent
                            icon={<LuTrash2 />}
                            width="w-1/6"
                            bgColor="red"
                            isDisabled={state.length <= 1}
                            onClick={() => handleOnRemove(containerIndex)}
                        />
                    </div>
                    <DividerComponent className="mt-2" />
                </div>
            ))}
            <ButtonComponent
                text={buttonText || TranslateConstants.ADD}
                className="!w-full sm:!w-1/3"
                isDisabled={!!state && state.length >= maxItems}
                onClick={handleOnAdd}
            />
        </>
    );
}

export default MultiInputsComponent;
