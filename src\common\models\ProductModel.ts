import { BaseModel } from ".";
import { ProductSizeTypeEnum } from "../enums/DataEnums";
import { IDeliveryAppProductModel } from "./DeliveryAppProductModel";

export interface IProductSizeModel extends BaseModel {
    name: string;
    price: number;
    deliveryApps?: IDeliveryAppProductModel;
}

export interface IProductModel extends BaseModel {
    number: number;
    name: string;
    price: number;
    image: string;
    isIncludingAdditions: boolean;
    categoryId: number;
    productSizeType: ProductSizeTypeEnum;
    isSubjectToTobaccoTax: boolean;
    active: boolean;
    sizes: IProductSizeModel[];
    deliveryApps?: IDeliveryAppProductModel;
}