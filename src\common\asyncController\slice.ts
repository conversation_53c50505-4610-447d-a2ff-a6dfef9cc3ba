import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { FetchState, FetchArgs } from "./interface";

// Async thunk for dynamic API fetching
export const fetchData = createAsyncThunk<any, FetchArgs<any>>(
    "fetch/data",
    async ({ key, promiseFn }, { rejectWithValue }) => {
        try {
            const data = await promiseFn();
            return { key, data };
        } catch (error: any) {
            return rejectWithValue({ key, error: error.message });
        }
    }
);

interface FetchSliceState {
    [key: string]: FetchState<any>;
}

const initialState: FetchSliceState = {};

const fetchSlice = createSlice({
    name: "fetch",
    initialState,
    reducers: {
        resetData: (state, action: PayloadAction<string>) => {
            delete state[action.payload];
        },
        updateCachedData: (state, action: PayloadAction<{
            key: string;
            operation: "add" | "update" | "updateOne" | "delete";
            selector?: (data: any) => any;
            result: any;
        }>) => {
            const { key, operation, selector } = action.payload;
            const data = state[key].data;
            if (!data) return;

            switch (operation) {
                case "add":
                    state[key].data = [action.payload.result, ...data];
                    break;
                case "update":
                    state[key].data = data.map((item: any) => {
                        if (selector && selector(item) === selector(action.payload.result)) {
                            return action.payload.result;
                        }
                        return item;
                    });
                    break;
                case "updateOne":
                    state[key].data = { ...data, ...action.payload.result };
                    break;
                case "delete":
                    state[key].data = data.filter((item: any) => selector && selector(item) !== selector(action.payload.result));
                    break;
                default:
                    break;
            }
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchData.pending, (state, action) => {
                state[action.meta.arg.key] = { data: undefined, isLoading: true, error: undefined, isError: false };
            })
            .addCase(fetchData.fulfilled, (state, action) => {
                state[action.payload.key] = {
                    data: action.payload.data,
                    isLoading: false,
                    error: undefined,
                    isError: false,
                };
            })
            .addCase(fetchData.rejected, (state, action) => {
                state[(action.payload as { key: string; error: string }).key] = {
                    data: undefined,
                    isLoading: false,
                    error: (action.payload as { key: string; error: string }).error,
                    isError: true,
                };
            });
    },
});

export const { resetData, updateCachedData } = fetchSlice.actions;
export default fetchSlice.reducer;
