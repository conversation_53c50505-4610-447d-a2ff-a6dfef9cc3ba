import { FC } from "react";
import AddAndFilterComponent from "../../../../../common/components/AddAndFilterComponent";
import ButtonComponent from "../../../../../common/components/ButtonComponent";
import SelectedButtonsComponent from "../../../../../common/components/SelectedButtonsComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { TranslateHelper } from "../../../../../common/helpers/TranslateHelper";
import {
    AdminDeliveryAppProductsView,
    AdminDeliveryAppProductsViewEnum,
} from "../AdminDeliveryAppsConstants";
import { IDeliveryApp } from "../../../../../common/interfaces";
import { IoMdArrowBack, IoMdArrowForward } from "react-icons/io";
import { useTranslate } from "../../../../../common/hooks/useTranslate";

interface IProps {
    onBack: () => void;
    item: IDeliveryApp;
    handleRefetch: () => void;
    handleOnSelect: (item: AdminDeliveryAppProductsViewEnum) => void;
    handleOnSave: () => void;
    isSaveDisabled?: boolean;
}

const AdminDeliveryAppsTopButtonsSlice: FC<IProps> = ({
    onBack,
    item,
    handleRefetch,
    handleOnSelect,
    handleOnSave,
    isSaveDisabled,
}) => {
    const { isRtl } = useTranslate();

    return (
        <div className="flex justify-between items-start flex-col-reverse sm:flex-row gap-2 w-full">
            <div className="w-full flex flex-col sm:w-1/2 md:w-2/5 lg:w-1/3 gap-2">
                <AddAndFilterComponent
                    onReload={handleRefetch}
                    onSave={handleOnSave}
                    isSaveDisabled={isSaveDisabled}
                    buttonsClassName="sm:!w-full md:!w-full lg:!w-full"
                />
                <SelectedButtonsComponent
                    items={AdminDeliveryAppProductsView}
                    textSelector={(item: string) => item}
                    defaultSelected={AdminDeliveryAppProductsViewEnum.PRODUCTS}
                    onSelect={handleOnSelect}
                />
            </div>
            <div className="flex justify-between sm:justify-end items-center gap-2 md:gap-5 w-full sm:w-1/2 md:w-2/5 lg:w-1/3">
                <div className="flex items-center gap-1 w-1/2 sm:justify-end">
                    <img className="h-10 w-10 rounded-full border" src={item.image} />
                    <div className="font-tajawal-bold">
                        {TranslateHelper.t(item.name)}
                    </div>
                </div>
                <ButtonComponent
                    text={TranslateConstants.BACK}
                    onClick={onBack}
                    className="!w-1/2 flex-row-reverse"
                    iconComponent={
                        isRtl ? (
                            <IoMdArrowBack className="text-lg" />
                        ) : (
                            <IoMdArrowForward className="text-lg" />
                        )
                    }
                    bgColor="slate"
                    borderColor="slate-600"
                />
            </div>
        </div>
    );
};

export default AdminDeliveryAppsTopButtonsSlice;
