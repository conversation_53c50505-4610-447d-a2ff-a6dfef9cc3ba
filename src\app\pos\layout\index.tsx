import { useEffect, useMemo, useState } from "react";
import GuardedRouteComponent from "../../../common/components/GuardedRouteComponent";
import SetRoutesComponent from "../../../common/components/SetRoutesComponent";
import useShowOrder from "../hooks/useShowOrder";
import { PosLayoutService } from "./PosLayoutService";
import PosLayoutLoadingFeature from "./features/PosLayoutLoadingFeature";
import usePosActions from "../redux/usePosActions";
import useActions from "../../../common/redux/data/useActions";
import PosOrderComponent from "../containers/order";
import PosHomeMenuFeature from "../pages/home/<USER>/PosHomeMenuFeature";
import OnlineStatusGuardComponent from "../../../common/components/OnlineStatusGuardComponent";
import { LocalStorageHelper } from "../../../common/helpers/LocalStorageHelper";
import { isPosInitKeyConstant } from "../../../common/constants/ConfigConstants";
import { FullScreenHelper } from "../../../common/helpers/FullScreenHelper";

const PosLayout = () => {
    const actions = useActions();
    const posActions = usePosActions();
    const { showOrder, showMenu } = useShowOrder();
    const [loading, setLoading] = useState({
        progress: 0,
        loading: false,
    });

    const init = async () => {
        await PosLayoutService.checkAppVersion();
        await PosLayoutService.init(setLoading, actions);
        await PosLayoutService.getAllData(posActions, actions);
    };

    useEffect(() => {
        FullScreenHelper.start();
        init()
        return () => { FullScreenHelper.exit() }
    }, []);

    const isInit = useMemo(() => {
        return !!LocalStorageHelper.get(isPosInitKeyConstant);
    }, [loading.loading]);

    if (loading.loading) {
        return <PosLayoutLoadingFeature progress={loading.progress} />;
    }

    return (
        <OnlineStatusGuardComponent guard={!isInit}>
            <GuardedRouteComponent authGuard={true}>
                <GuardedRouteComponent isPosGuard={true}>
                    <div
                        className="grid grid-cols-12 h-s font-tajawal-medium bg-base-200"
                        dir="rtl"
                        data-theme="winter"
                    >
                        <div className={showOrder ? "col-span-9" : "col-span-12"}>
                            <SetRoutesComponent type="pos" />
                            {showMenu && <PosHomeMenuFeature />}
                        </div>
                        {showOrder && (
                            <div className="col-span-3">
                                <PosOrderComponent />
                            </div>
                        )}
                    </div>
                </GuardedRouteComponent>
            </GuardedRouteComponent>
        </OnlineStatusGuardComponent>
    );
};

export default PosLayout;
