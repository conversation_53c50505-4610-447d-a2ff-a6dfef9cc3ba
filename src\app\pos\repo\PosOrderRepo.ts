import {
    APP_LOCAL_DB_COLLECTIONS,
    AppLocalDB,
} from "../../../common/config/localDB";
import { EndPointsConstants } from "../../../common/constants/ApiConstants";
import { AxiosHelper } from "../../../common/helpers/AxiosHelper";
import { ShiftHelper } from "../../../common/helpers/ShiftHelper";
import { IOrderBody } from "../../../common/interfaces/order";
import {
    ILocalDBGetOptionsProperties,
    IOperator,
} from "../../../common/LocalDB/LocalDBInterface";
import { IPosOrderModel } from "../../../common/models/PosOrderModel";
import { orderUtils } from "../../../common/utils/OrderUtils";
import { debug } from "../../../common/utils/CommonUtils";
import { IPosOrder } from "../interface";

export class PosOrderRepo {
    static addOrder = async (order: IPosOrder): Promise<IPosOrderModel> => {
        try {
            const shift = ShiftHelper.get();
            if (!shift) throw new Error("Shift not found");
            const startTime = new Date().getTime();

            const body: IPosOrderModel = {
                ...order,
                id: AppLocalDB.generateId(),
                products: order.products.map((product) => {
                    const { subTotal, vat, total, discount, tobaccoTax } = orderUtils.getPosProductAmounts(
                        product.price,
                        product.quantity,
                        order.selectedDiscount?.amount,
                        product.isSubjectToTobaccoTax
                    );
                    return {
                        ...product,
                        additions: product.additions?.map((ad) => {
                            const additionTotalPrice = ad.price * ad.quantity;
                            const { subTotal, vat, total, discount } = orderUtils.getAmounts(
                                additionTotalPrice,
                                order.selectedDiscount?.amount
                            );
                            return { ...ad, subTotal, discount, vat, total, startTime: ad.startTime ?? new Date().getTime() };
                        }),
                        subTotal,
                        discount,
                        tobaccoTax,
                        vat,
                        total,
                        startTime: order.startTime?.getTime() ?? startTime,
                    };
                }),
                shiftId: shift.shiftId,
                deleted: false,
                createdAt: new Date(),
                updatedAt: new Date(),
            };
            if (!body.products.length) throw new Error("No products found");
            await AppLocalDB.add(APP_LOCAL_DB_COLLECTIONS.ORDERS, body);
            return body;
        } catch (error) {
            debug(`PosOrderRepo [addOrder] Error: ${error}`);
            throw error;
        }
    };

    static getAndCountOrders = async (
        where?: [keyof IPosOrderModel, IOperator, any]
    ) => {
        try {
            const shift = ShiftHelper.get();
            if (!shift) {
                return { data: [], count: 0 };
            }

            const orderWere: [keyof IPosOrderModel, IOperator, any][] = [
                [`shiftId`, "==", shift.shiftId],
            ];
            if (where) orderWere.push(where);

            const res = await AppLocalDB.getAndCount<IPosOrderModel>(
                APP_LOCAL_DB_COLLECTIONS.ORDERS,
                {
                    where: [orderWere],
                }
            );
            return res;
        } catch (error) {
            debug(`PosOrderRepo [getOrdersCount] Error: ${error}`);
            throw error;
        }
    };

    static getShiftOrders = async (): Promise<{ data: IPosOrderModel[] }> => {
        try {
            const shift = ShiftHelper.get();
            if (!shift) {
                return { data: [] };
            }

            const res = await AppLocalDB.get<IPosOrderModel>(
                APP_LOCAL_DB_COLLECTIONS.ORDERS,
                {
                    orderBy: ["orderNumber", "desc"],
                    where: [[[`shiftId`, "===", shift.shiftId]]],
                }
            );
            return { data: res };
        } catch (error) {
            debug(`PosOrderRepo [getOrders] Error: ${error}`);
            throw error;
        }
    };

    static updateOrder = async (
        order: IPosOrderModel
    ): Promise<IPosOrderModel> => {
        try {
            const { subTotal, vat, total, discount, tobaccoTax } =
                orderUtils.getPosOrderData(order);
            const startTime = new Date().getTime();

            const body = {
                ...order,
                updatedAt: new Date(),
                products: order.products.map((el) => {
                    const { subTotal, vat, total, discount, tobaccoTax } = orderUtils.getPosProductAmounts(
                        el.price,
                        el.quantity,
                        order.selectedDiscount?.amount,
                        el.isSubjectToTobaccoTax
                    );
                    return {
                        ...el,
                        additions: el.additions?.map((ad) => {
                            const additionTotalPrice = ad.price * ad.quantity;
                            const { subTotal, vat, total, discount } = orderUtils.getAmounts(
                                additionTotalPrice,
                                order.selectedDiscount?.amount
                            );
                            return { ...ad, subTotal, discount, vat, total, startTime: ad.startTime ?? order.startTime?.getTime() ?? startTime };
                        }),
                        subTotal,
                        discount,
                        tobaccoTax,
                        vat,
                        total,
                        startTime: el.startTime ?? order.startTime?.getTime() ?? startTime,
                    };
                }),
                subTotal,
                vat,
                total,
                discount,
                tobaccoTax,
            };
            await AppLocalDB.update(APP_LOCAL_DB_COLLECTIONS.ORDERS, order.id, body);
            return order;
        } catch (error) {
            debug(`PosOrderRepo [updateOrder] Error: ${error}`);
            throw error;
        }
    };

    static async uploadOrders(body: IOrderBody[]) {
        try {
            const res = await AxiosHelper.post(EndPointsConstants.ORDERS, body);
            if (!res.success) throw new Error(res.message);
        } catch (error) {
            debug(`PosOrderRepo [uploadOrders] Error: `, error);
            throw error;
        }
    }

    static async ordersCount() {
        try {
            return await AppLocalDB.count(APP_LOCAL_DB_COLLECTIONS.ORDERS);
        } catch (error) {
            debug(`PosOrderRepo [ordersCount] Error: `, error);
            throw error;
        }
    }

    static async getOrders(
        options?: ILocalDBGetOptionsProperties<IPosOrderModel>
    ) {
        try {
            return await AppLocalDB.get<IPosOrderModel>(
                APP_LOCAL_DB_COLLECTIONS.ORDERS,
                options
            );
        } catch (error) {
            debug(`PosOrderRepo [getOrders] Error: `, error);
            throw error;
        }
    }

    static async deleteAllOrders() {
        try {
            await AppLocalDB.deleteAll(APP_LOCAL_DB_COLLECTIONS.ORDERS);
        } catch (error) {
            debug(`PosOrderRepo [deleteAllOrders] Error: `, error);
            throw error;
        }
    }

    static async getOrdersCount() {
        try {
            const res = await AxiosHelper.get<number>(
                EndPointsConstants.ORDERS_COUNT
            );

            if (!res.success) throw new Error(res.message);

            return res.data || 0;
        } catch (error) {
            debug(`PosOrderRepo [ordersCount] Error: `, error);
            throw error;
        }
    }

    static async deleteOrder(id: number) {
        try {
            await AppLocalDB.deleteById(APP_LOCAL_DB_COLLECTIONS.ORDERS, id);
        } catch (error) {
            debug(`PosOrderRepo [deleteOrder] Error: `, error);
            throw error;
        }
    }
}
