import { IModalProps } from "../../components/ModalComponent";
import {
    setLoadingAction,
    setModalAction,
    closeModalAction,
    setUpdatesCountAction,
} from "./slice";
import { useAppDispatch } from "../store";
import { IOrganizationUpdatesCountModel } from "../../models/OrganizationUpdatesCountModel";

export default function useActions(): IActions {
    const dispatch = useAppDispatch();

    return {
        openModal: (payload: Partial<IModalProps>) => {
            dispatch(setModalAction({ ...payload, show: true }));
        },
        closeModal: () => {
            dispatch(closeModalAction());
        },
        setLoading: (loading = true) => {
            dispatch(setLoadingAction(loading));
        },
        setUpdatesCount: (payload: Partial<IOrganizationUpdatesCountModel>) => {
            dispatch(setUpdatesCountAction(payload));
        },
    };
}

export type IActions = {
    openModal: (payload: Partial<IModalProps>) => void;
    closeModal: () => void;
    setLoading: (loading?: boolean) => void;
    setUpdatesCount: (payload: Partial<IOrganizationUpdatesCountModel>) => void;
};
