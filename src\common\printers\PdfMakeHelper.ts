import { Content, TDocumentDefinitions } from "pdfmake/interfaces";
import { imageUrlToBase64, isArabic } from "../utils/CommonUtils";
import { DateUtils } from "../utils/DateUtils";
import { OrganizationHelper } from "../helpers/OrganizationHelper";
import { PdfMakeProps } from "./PdfMakeInterfaces";

export class PdfMakeHelper {
    static defaultProps = async (
        content: Content,
        isPrint = false,
        options: PdfMakeProps = { isLandScape: false }
    ): Promise<TDocumentDefinitions> => {
        content = this.handleTextDirectionForArabic(content);

        return {
            content,
            pageSize: {
                width: 842,  // A4 landscape width (in points)
                height: 595,
            },
            pageOrientation: options.isLandScape ? "landscape" : "portrait",
            defaultStyle: {
                font: "Tajawal",
                alignment: "center",
                color: "#000",
                fontSize: 12,
            },
            styles: {
                tableHeader: {
                    bold: true,
                    fillColor: "#1F618D",
                    color: "#fff",
                    fontSize: 12,
                },
                table: {
                    fontSize: 10,
                    fillColor: "#EBF5FB",
                },
            },
            pageMargins: [
                10, // left
                80, // top
                options.isLandScape ? isPrint ? 60 : 60 : 5, // right
                isPrint ? options.isLandScape ? 20 : 80 : 20 // bottom
            ],
            header: this.handleTextDirectionForArabic(
                {
                    columns: [
                        {
                            stack: [
                                this.optionalDocItem(!!OrganizationHelper.getOrganization()?.name, [
                                    { text: OrganizationHelper.getOrganization()?.name || "", style: { fontSize: 14, bold: true } },
                                ]),
                                this.optionalDocItem(!!OrganizationHelper.getOrganization()?.taxNumber, [
                                    { text: `الرقم الضريبي: ${OrganizationHelper.getOrganization()?.taxNumber || ""}`, style: { fontSize: 12 } },
                                ]),
                                this.optionalDocItem(!!OrganizationHelper.getOrganization()?.registrationNumber, [
                                    { text: `رقم السجل التجاري: ${OrganizationHelper.getOrganization()?.registrationNumber || ""}`, style: { fontSize: 12 } },
                                ]),
                            ],
                            alignment: 'right',
                            style: { lineHeight: 1.5 },
                            width: "85%",
                            margin: [0, 10, 0, 0],
                        },
                        this.optionalDocItem(!!OrganizationHelper.getOrganization()?.logo, [
                            {
                                image: await imageUrlToBase64(OrganizationHelper.getOrganization()?.logo || ""),
                                width: 60,
                                height: 60,
                                margin: [0, 5, options.isLandScape ? 15 : 0, 0],
                                alignment: !options.isLandScape ? "right" : undefined,
                            },
                        ]),
                    ],
                    margin: [10, 10, 20, 0],
                }
            ),
            footer: (currentPage: number, pageCount: number) => {
                return {
                    columns: [
                        {
                            text: DateUtils.format(Date.now(), "dd-MM-yyyy hh:mm A", true),
                            alignment: "left",
                            style: "small",
                            margin: [10, isPrint ? 5 : 0, 0, 0],
                        },
                        {
                            text: `${currentPage} / ${pageCount}`,
                            alignment: "right",
                            style: "small",
                            margin: [0, isPrint ? 5 : 0, options.isLandScape ? 65 : 10, 0],
                        },
                    ],
                };
            },
        };
    };

    static optionalDocItem = (
        condition: boolean,
        item: Content[],
    ): Content[] => {
        return condition ? [...item] : [];
    };

    static printDivider = (
        isVertical = false,
        bold = false,
        margin = 0
    ): Content => {
        if (isVertical) {
            return {
                stack: Array.from({ length: 5 }).map(() => ({
                    text: "|",
                    bold,
                    margin: [margin, 0, margin, 0],
                })),
            };
        }

        return {
            text: '-'.repeat(bold ? 72 : 78),
            bold,
            margin: [0, margin, 0, margin],
        };
    };

    static reverseTextIfArabic = (text: string) => {
        function reverseArabicText(text: string): string {
            return ` ${text}`.split(" ").reverse().join(" ");
        }

        if (isArabic(text)) {
            return reverseArabicText(text);
        }
        return text;
    };

    private static handleTextDirectionForArabic = (
        content: Content[] | Content
    ): Content[] => {

        const processText = (c: any) => {
            if (c.text) {
                c.text = this.reverseTextIfArabic(c.text);
            } else if (c.columns) {
                c.columns = c.columns.map((item: any) =>
                    item.text || item[0]?.text ? { ...(item[0] || item), text: this.reverseTextIfArabic(item.text || item[0].text) } : processText(item)
                );
            } else if (c.stack) {
                c.stack = c.stack.map((item: any) =>
                    item.text || item[0]?.text ? { ...(item[0] || item), text: this.reverseTextIfArabic(item.text || item[0].text) } : processText(item)
                );
            } else if (c.table) {
                console.log(c.table);

                c.table.body = c.table.body.map((row: any) =>
                    row.map((col: any) => {
                        if (col.text) {
                            return { ...col, text: this.reverseTextIfArabic(col.text) };
                        } else if (Array.isArray(col)) {
                            return col.map((subCol: any) =>
                                subCol?.text
                                    ? { ...subCol, text: this.reverseTextIfArabic(subCol?.text) }
                                    : subCol
                            );
                        } else {
                            return col;
                        }
                    })
                );
            }
            return c;
        };

        if (Array.isArray(content)) return content.map(processText);
        else return [processText(content)];
    };
}
