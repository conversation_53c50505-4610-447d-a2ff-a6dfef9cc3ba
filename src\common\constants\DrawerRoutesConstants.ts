import { OrganizationHelper } from "../helpers/OrganizationHelper";
import { RoutsConstants } from "./RoutesConstants";
import { TranslateConstants } from "./TranslateConstants";

export const DrawerRoutesConstants = () => {
    const hasTobaccoTax = OrganizationHelper.hasTobaccoTax();

    return [
        {
            path: RoutsConstants.admin.categories.fullPath,
            name: TranslateConstants.CATEGORIES,
        },
        {
            path: RoutsConstants.admin.additions.fullPath,
            name: TranslateConstants.ADDITIONS,
        },
        {
            path: RoutsConstants.admin.products.fullPath,
            name: TranslateConstants.PRODUCTS,
        },
        {
            path: RoutsConstants.admin.tables.fullPath,
            name: TranslateConstants.TABLES,
        },
        {
            path: RoutsConstants.admin.discounts.fullPath,
            name: TranslateConstants.DISCOUNTS,
        },
        {
            path: RoutsConstants.admin.customers.fullPath,
            name: TranslateConstants.CUSTOMERS,
        },
        {
            name: TranslateConstants.REPORTS,
            submenu: [
                {
                    path: RoutsConstants.admin.shiftReport.fullPath,
                    name: TranslateConstants.SHIFT_REPORT,
                },
                {
                    path: RoutsConstants.admin.salesReport.fullPath,
                    name: TranslateConstants.SALES_REPORT,
                },
                {
                    path: RoutsConstants.admin.mostSellingProductsReport.fullPath,
                    name: TranslateConstants.MOST_SELLING_PRODUCTS_REPORT,
                },
                {
                    path: RoutsConstants.admin.additionsReport.fullPath,
                    name: TranslateConstants.ADDITIONS_REPORT,
                },
                ...(hasTobaccoTax ? [{
                    path: RoutsConstants.admin.tobaccoTaxReport.fullPath,
                    name: TranslateConstants.TOBACCO_TAX_REPORT,
                }] : []),
                {
                    path: RoutsConstants.admin.customersReport.fullPath,
                    name: TranslateConstants.CUSTOMERS_REPORT,
                },
            ]
        },
        {
            path: RoutsConstants.admin.settings.fullPath,
            name: TranslateConstants.SETTINGS,
        },
    ];
}