import { IAdminProductSizeInputs } from "../../../app/admin/pages/products/AdminProductsInterface";
import { ProductSizeTypeEnum } from "../../enums/DataEnums";

export interface IProductBody {
    name: string;
    price: number;
    image: string;
    isIncludingAdditions: boolean;
    categoryId: number;
    productSizeType: ProductSizeTypeEnum;
    isSubjectToTobaccoTax: boolean;
    active: boolean;
    sizes: IAdminProductSizeInputs[];
}