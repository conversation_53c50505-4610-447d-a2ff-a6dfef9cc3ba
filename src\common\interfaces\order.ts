import { DeliveryAppEnum, OrderStatusEnum, OrderTypeEnum } from "../enums/DataEnums";

export interface IOrderProductAdditions {
    additionId: number;
    quantity: number;
    price: number;
    name: string;
    subTotal: number;
    discount: number;
    vat: number;
    total: number;
    startTime: number;
}

export interface IOrderProductBody {
    productId: number;
    quantity: number;
    price: number;
    name: string;
    isSubjectToTobaccoTax: boolean;
    subTotal: number;
    discount: number;
    tobaccoTax: number;
    vat: number;
    total: number;
    startTime: number;
    orderProductAdditions?: IOrderProductAdditions[]
}

export interface IOrderBody {
    shiftId: number;
    type: OrderTypeEnum;
    deliveryApp?: DeliveryAppEnum;
    orderProducts: IOrderProductBody[];
    tableId?: number;
    customerId?: number;
    selectedDiscountId?: number;
    status: OrderStatusEnum;
    invoiceNumber: string;
    orderNumber: string;
    subTotal: number;
    discount: number;
    vat: number;
    tobaccoTax: number;
    total: number;
    deliveryAppFee: number;
    totalDue: number;
    cash: number;
    network: number;
    startTime: number;
    endTime: number;
}