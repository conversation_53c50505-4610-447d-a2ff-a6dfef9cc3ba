import DropDownSearchComponent from "../../../../../common/components/DropDownSearchComponent";
import { TranslateConstants } from "../../../../../common/constants/TranslateConstants";
import { useTranslate } from "../../../../../common/hooks/useTranslate";
import { FC, useEffect, useState } from "react";
import { IPrinterModel } from "../../../../../common/models/PrinterModel";
import { useAppSelector } from "../../../../../common/redux/store";
import { posCategoriesSelector } from "../../../redux/selector";
import ButtonComponent from "../../../../../common/components/ButtonComponent";
import DividerComponent from "../../../../../common/components/DividerComponent";
import { PosSettingsValidation } from "../PosSettingsValidation";
import { IPrinterData } from "../../../../../common/interfaces";
import { PosSettingsService } from "../PosSettingsService";

interface IProps {
    printers: IPrinterModel[];
}

const PosPrinterSettingsFeature: FC<IProps> = ({ printers }) => {
    const { translate } = useTranslate();
    const categories = useAppSelector(posCategoriesSelector);
    const [data, setData] = useState<IPrinterData[]>([]);
    const [disabled, setDisabled] = useState<boolean>(true);

    useEffect(() => {
        PosSettingsService.initPrintersData(setData, categories);
    }, [categories]);

    useEffect(() => {
        const isDeeplyEqual = PosSettingsValidation.checkPrintersDeeplyEqual(data);
        setDisabled(isDeeplyEqual);
    }, [data, disabled]);

    const handleOnSave = () => PosSettingsService.onSave(data, setDisabled);
    const handleOnSelect = (value: IPrinterModel, index: number) =>
        PosSettingsService.handleOnPrinterSelect(setData, index, value);

    return (
        <div className="bg-white rounded-xl shadow p-2 border">
            <div className="text-lg font-tajawal-bold">
                {translate(TranslateConstants.PRINTER_SETTINGS)}
            </div>
            <DividerComponent className="my-2" />
            <div className="grid grid-cols-1 lg:grid-cols-2 my-2 gap-2 gap-x-5">
                {data?.map((item, index) => (
                    <div key={index}>
                        <label>{translate(item.category)}</label>
                        <DropDownSearchComponent
                            placeholder={TranslateConstants.PRINTER_NAME}
                            isSearchable={false}
                            items={printers}
                            titleSelector={(item: IPrinterModel) => item.deviceId}
                            defaultValue={item.printer ? translate(item.printer) : undefined}
                            onSelect={(value) => handleOnSelect(value, index)}
                            isNullable={false}
                        />
                    </div>
                ))}
            </div>
            <DividerComponent className="my-4" />
            <ButtonComponent
                text={translate(TranslateConstants.SAVE)}
                className="lg:!w-1/4"
                onClick={handleOnSave}
                isDisabled={disabled}
            />
        </div>
    );
};

export default PosPrinterSettingsFeature;
